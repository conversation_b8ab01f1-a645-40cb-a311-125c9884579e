"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/homepage-settings/route";
exports.ids = ["app/api/homepage-settings/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_homepage_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/homepage-settings/route.ts */ \"(rsc)/./app/api/homepage-settings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/homepage-settings/route\",\n        pathname: \"/api/homepage-settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/homepage-settings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\homepage-settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_homepage_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/homepage-settings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/homepage-settings/route.ts":
/*!********************************************!*\
  !*** ./app/api/homepage-settings/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n\n\n\n\n// GET /api/homepage-settings - Get homepage settings\nasync function GET() {\n    try {\n        // Get homepage settings\n        const settings = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.homepageSetting.findMany({\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Get featured product (Product of the Month)\n        // First check if there's a specific product set in homepage settings\n        let featuredProduct = null;\n        if (settings.length > 0 && settings[0].productOfTheMonthId) {\n            featuredProduct = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n                where: {\n                    id: settings[0].productOfTheMonthId,\n                    isActive: true\n                },\n                include: {\n                    images: true,\n                    category: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                }\n            });\n        }\n        // If no specific product is set, fall back to the first featured product\n        if (!featuredProduct) {\n            featuredProduct = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findFirst({\n                where: {\n                    isFeatured: true,\n                    isActive: true\n                },\n                include: {\n                    images: true,\n                    category: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                }\n            });\n        }\n        // Get bestsellers - either manually selected or auto-selected\n        let bestsellers = [];\n        if (settings.length > 0 && settings[0].bestsellerIds && settings[0].bestsellerIds.length > 0) {\n            // Get manually selected bestsellers\n            const bestsellerProducts = await Promise.all(settings[0].bestsellerIds.map(async (id)=>{\n                return await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n                    where: {\n                        id,\n                        isActive: true\n                    },\n                    include: {\n                        images: true,\n                        category: true,\n                        _count: {\n                            select: {\n                                reviews: true\n                            }\n                        }\n                    }\n                });\n            }));\n            // Filter out null results and maintain order\n            bestsellers = bestsellerProducts.filter(Boolean);\n        } else {\n            // Auto-select bestsellers (top 4 products by sales/reviews)\n            bestsellers = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n                where: {\n                    isActive: true\n                },\n                include: {\n                    images: true,\n                    category: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                },\n                orderBy: {\n                    reviews: {\n                        _count: \"desc\"\n                    }\n                },\n                take: 4\n            });\n        }\n        // Get categories for showcase\n        const categories = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.category.findMany({\n            where: {\n                isActive: true\n            },\n            include: {\n                _count: {\n                    select: {\n                        products: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            },\n            take: 6\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                settings: settings.length > 0 ? settings[0] : null,\n                featuredProduct,\n                bestsellers,\n                categories\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching homepage settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch homepage settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/homepage-settings - Create or update homepage settings\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_app_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        // Check if user is admin\n        if (!session || session.user.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { // Hero Section\n        heroTitle, heroSubtitle, heroCtaText, heroCtaLink, heroBackgroundColor, showHero, // Product of the Month\n        productOfTheMonthId, showProductOfMonth, // Promotional Banner\n        bannerText, bannerCtaText, bannerCtaLink, bannerBackgroundColor, showBanner, // Sections\n        showCategories, productSectionBgColor, bestsellerIds, showBestsellers, // Newsletter\n        newsletterTitle, newsletterSubtitle, showNewsletter, // Trust Badges\n        showTrustBadges, // Flash Sale Section\n        flashSaleTitle, flashSaleSubtitle, flashSaleEndDate, flashSaleBackgroundColor, flashSalePercentage, showFlashSale, // Testimonials Section\n        testimonialsTitle, testimonialsSubtitle, testimonialsBackgroundColor, showTestimonials, isActive } = body;\n        // Create or update homepage settings\n        const settings = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.homepageSetting.upsert({\n            where: {\n                id: \"homepage-settings\"\n            },\n            update: {\n                // Hero Section\n                ...heroTitle !== undefined && {\n                    heroTitle\n                },\n                ...heroSubtitle !== undefined && {\n                    heroSubtitle\n                },\n                ...heroCtaText !== undefined && {\n                    heroCtaText\n                },\n                ...heroCtaLink !== undefined && {\n                    heroCtaLink\n                },\n                ...heroBackgroundColor !== undefined && {\n                    heroBackgroundColor\n                },\n                ...showHero !== undefined && {\n                    showHero\n                },\n                // Product of the Month\n                ...productOfTheMonthId !== undefined && {\n                    productOfTheMonthId\n                },\n                ...showProductOfMonth !== undefined && {\n                    showProductOfMonth\n                },\n                // Promotional Banner\n                ...bannerText !== undefined && {\n                    bannerText\n                },\n                ...bannerCtaText !== undefined && {\n                    bannerCtaText\n                },\n                ...bannerCtaLink !== undefined && {\n                    bannerCtaLink\n                },\n                ...bannerBackgroundColor !== undefined && {\n                    bannerBackgroundColor\n                },\n                ...showBanner !== undefined && {\n                    showBanner\n                },\n                // Sections\n                ...showCategories !== undefined && {\n                    showCategories\n                },\n                ...productSectionBgColor !== undefined && {\n                    productSectionBgColor\n                },\n                ...bestsellerIds !== undefined && {\n                    bestsellerIds\n                },\n                ...showBestsellers !== undefined && {\n                    showBestsellers\n                },\n                // Newsletter\n                ...newsletterTitle !== undefined && {\n                    newsletterTitle\n                },\n                ...newsletterSubtitle !== undefined && {\n                    newsletterSubtitle\n                },\n                ...showNewsletter !== undefined && {\n                    showNewsletter\n                },\n                // Trust Badges\n                ...showTrustBadges !== undefined && {\n                    showTrustBadges\n                },\n                // Flash Sale Section\n                ...flashSaleTitle !== undefined && {\n                    flashSaleTitle\n                },\n                ...flashSaleSubtitle !== undefined && {\n                    flashSaleSubtitle\n                },\n                ...flashSaleEndDate !== undefined && {\n                    flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null\n                },\n                ...flashSaleBackgroundColor !== undefined && {\n                    flashSaleBackgroundColor\n                },\n                ...flashSalePercentage !== undefined && {\n                    flashSalePercentage\n                },\n                ...showFlashSale !== undefined && {\n                    showFlashSale\n                },\n                // Testimonials Section\n                ...testimonialsTitle !== undefined && {\n                    testimonialsTitle\n                },\n                ...testimonialsSubtitle !== undefined && {\n                    testimonialsSubtitle\n                },\n                ...testimonialsBackgroundColor !== undefined && {\n                    testimonialsBackgroundColor\n                },\n                ...showTestimonials !== undefined && {\n                    showTestimonials\n                },\n                ...isActive !== undefined && {\n                    isActive\n                },\n                updatedAt: new Date()\n            },\n            create: {\n                id: \"homepage-settings\",\n                // Hero Section\n                heroTitle: heroTitle || \"Natural Skincare Essentials\",\n                heroSubtitle: heroSubtitle || \"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin\",\n                heroCtaText: heroCtaText || \"Shop Collection\",\n                heroCtaLink: heroCtaLink || \"/shop\",\n                heroBackgroundColor: heroBackgroundColor || \"#f0fdf4\",\n                showHero: showHero !== undefined ? showHero : true,\n                // Product of the Month\n                productOfTheMonthId,\n                showProductOfMonth: showProductOfMonth !== undefined ? showProductOfMonth : true,\n                // Promotional Banner\n                bannerText,\n                bannerCtaText,\n                bannerCtaLink,\n                bannerBackgroundColor: bannerBackgroundColor || \"#22c55e\",\n                showBanner: showBanner !== undefined ? showBanner : true,\n                // Sections\n                showCategories: showCategories !== undefined ? showCategories : true,\n                productSectionBgColor: productSectionBgColor || \"#f0fdf4\",\n                bestsellerIds: bestsellerIds || [],\n                showBestsellers: showBestsellers !== undefined ? showBestsellers : true,\n                // Newsletter\n                newsletterTitle: newsletterTitle || \"Stay Updated\",\n                newsletterSubtitle: newsletterSubtitle || \"Get the latest updates on new products and exclusive offers\",\n                showNewsletter: showNewsletter !== undefined ? showNewsletter : true,\n                // Trust Badges\n                showTrustBadges: showTrustBadges !== undefined ? showTrustBadges : true,\n                // Flash Sale Section\n                flashSaleTitle: flashSaleTitle || \"Weekend Flash Sale\",\n                flashSaleSubtitle: flashSaleSubtitle || \"Get 25% off all natural skincare products\",\n                flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null,\n                flashSaleBackgroundColor: flashSaleBackgroundColor || \"#16a34a\",\n                flashSalePercentage: flashSalePercentage || 25,\n                showFlashSale: showFlashSale !== undefined ? showFlashSale : true,\n                // Testimonials Section\n                testimonialsTitle: testimonialsTitle || \"What Our Customers Say\",\n                testimonialsSubtitle: testimonialsSubtitle || \"Real reviews from real customers who love our natural skincare\",\n                testimonialsBackgroundColor: testimonialsBackgroundColor || \"#f0fdf4\",\n                showTestimonials: showTestimonials !== undefined ? showTestimonials : true,\n                isActive: isActive !== undefined ? isActive : true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: settings,\n            message: \"Homepage settings updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating homepage settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to update homepage settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/homepage-settings/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_db__WEBPACK_IMPORTED_MODULE_4__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // If this is the first time the user signs in\n            if (user) {\n                token.sub = user.id;\n                token.role = user.role;\n            }\n            // For OAuth providers, ensure we get the correct user ID from database\n            if (account && token.email) {\n                try {\n                    const dbUser = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: token.email\n                        },\n                        select: {\n                            id: true,\n                            role: true\n                        }\n                    });\n                    if (dbUser) {\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                    }\n                } catch (error) {\n                    // Log error in development only\n                    if (true) {\n                        console.error(\"Error fetching user in JWT callback:\", error);\n                    }\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Always try to get fresh user data from database\n            if (token.email) {\n                try {\n                    const dbUser = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: token.email\n                        },\n                        select: {\n                            id: true,\n                            role: true,\n                            email: true,\n                            name: true\n                        }\n                    });\n                    if (dbUser) {\n                        return {\n                            ...session,\n                            user: {\n                                ...session.user,\n                                id: dbUser.id,\n                                role: dbUser.role,\n                                email: dbUser.email,\n                                name: dbUser.name\n                            }\n                        };\n                    }\n                } catch (error) {\n                    // Log error in development only\n                    if (true) {\n                        console.error(\"Error fetching user in session callback:\", error);\n                    }\n                }\n            }\n            // Fallback: if we have token.sub but no database lookup worked\n            if (session.user && token.sub) {\n                return {\n                    ...session,\n                    user: {\n                        ...session.user,\n                        id: token.sub,\n                        role: token.role\n                    }\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();