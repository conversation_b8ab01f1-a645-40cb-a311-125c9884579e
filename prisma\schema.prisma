generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String          @id @default(cuid())
  email            String          @unique
  name             String?
  phone            String?
  avatar           String?
  password         String?
  role             UserRole        @default(CUSTOMER)
  emailVerified    DateTime?
  resetToken       String?
  resetTokenExpiry DateTime?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  addresses        Address[]
  couponUsages     CouponUsage[]
  notifications    Notification[]
  orders           Order[]
  reviews          Review[]
  preferences      UserPreference?
  wishlist         WishlistItem[]

  @@map("users")
}

model UserPreference {
  id                    String   @id @default(cuid())
  userId                String   @unique
  language              String   @default("en-US")
  theme                 String   @default("light")
  orderUpdates          <PERSON>olean  @default(true)
  promotions            Boolean  @default(false)
  newsletter            Boolean  @default(true)
  smsNotifications      Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  adminMessages         <PERSON>olean  @default(true)
  broadcastMessages     <PERSON>olean  @default(true)
  emailNotifications    Boolean  @default(true)
  inAppNotifications    Boolean  @default(true)
  orderNotifications    Boolean  @default(true)
  priceDropAlerts       Boolean  @default(false)
  reviewNotifications   Boolean  @default(true)
  wishlistNotifications Boolean  @default(true)
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model Category {
  id                String            @id @default(cuid())
  name              String            @unique
  slug              String            @unique
  description       String?
  parentId          String?
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  parent            Category?         @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children          Category[]        @relation("CategoryHierarchy")
  productCategories ProductCategory[]
  products          Product[]

  @@map("categories")
}

model Product {
  id                String            @id @default(cuid())
  name              String
  slug              String            @unique
  description       String?
  shortDescription  String?
  comparePrice      Float?
  costPrice         Float?
  weight            Float?
  dimensions        String?
  isActive          Boolean           @default(true)
  isFeatured        Boolean           @default(false)
  metaTitle         String?
  metaDescription   String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  categoryId        String?
  price             Float?
  homepageSettings  HomepageSetting[] @relation("ProductOfTheMonth")
  orderItems        OrderItem[]
  productCategories ProductCategory[]
  faqs              ProductFAQ[]
  images            ProductImage[]
  variants          ProductVariant[]
  category          Category?         @relation(fields: [categoryId], references: [id])
  reviews           Review[]
  wishlist          WishlistItem[]

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  alt       String?
  position  Int     @default(0)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id          String      @id @default(cuid())
  name        String
  value       String
  price       Float?
  productId   String
  pricingMode PricingMode @default(INCREMENT)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

model ProductFAQ {
  id        String   @id @default(cuid())
  question  String
  answer    String
  position  Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_faqs")
}

model Order {
  id                String        @id @default(cuid())
  orderNumber       String        @unique
  status            OrderStatus   @default(PENDING)
  paymentStatus     PaymentStatus @default(PENDING)
  paymentMethod     String?
  paymentId         String?
  subtotal          Float
  tax               Float         @default(0)
  shipping          Float         @default(0)
  discount          Float         @default(0)
  total             Float
  currency          String        @default("INR")
  notes             String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  userId            String
  couponCodes       String[]
  couponDiscount    Float         @default(0)
  estimatedDelivery String?
  trackingNumber    String?
  couponUsages      CouponUsage[]
  address           OrderAddress?
  items             OrderItem[]
  user              User          @relation(fields: [userId], references: [id])

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Float
  total     Float
  orderId   String
  productId String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model OrderAddress {
  id         String  @id @default(cuid())
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String  @default("India")
  phone      String?
  orderId    String  @unique
  order      Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_addresses")
}

model Address {
  id         String  @id @default(cuid())
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String  @default("India")
  phone      String?
  isDefault  Boolean @default(false)
  userId     String
  user       User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

model Review {
  id         String       @id @default(cuid())
  rating     Int
  title      String?
  content    String?
  isVerified Boolean      @default(false)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  userId     String
  productId  String
  status     ReviewStatus @default(PENDING)
  product    Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  user       User         @relation(fields: [userId], references: [id])

  @@unique([userId, productId])
  @@map("reviews")
}

model WishlistItem {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  userId    String
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

model ProductCategory {
  id         String   @id @default(cuid())
  productId  String
  categoryId String
  createdAt  DateTime @default(now())
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, categoryId])
  @@map("product_categories")
}

model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String @default("string")

  @@map("settings")
}

model Coupon {
  id                   String        @id @default(cuid())
  code                 String        @unique
  name                 String
  description          String?
  type                 CouponType
  discountType         DiscountType
  discountValue        Float
  minimumAmount        Float?
  maximumDiscount      Float?
  usageLimit           Int?
  usageCount           Int           @default(0)
  userUsageLimit       Int?
  isActive             Boolean       @default(true)
  isStackable          Boolean       @default(false)
  validFrom            DateTime      @default(now())
  validUntil           DateTime?
  applicableProducts   String[]
  applicableCategories String[]
  excludedProducts     String[]
  excludedCategories   String[]
  customerSegments     String[]
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  showInModule         Boolean       @default(false)
  usages               CouponUsage[]

  @@map("coupons")
}

model CouponUsage {
  id       String   @id @default(cuid())
  couponId String
  userId   String
  orderId  String?
  usedAt   DateTime @default(now())
  coupon   Coupon   @relation(fields: [couponId], references: [id], onDelete: Cascade)
  order    Order?   @relation(fields: [orderId], references: [id])
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([couponId, userId, orderId])
  @@map("coupon_usages")
}

model HomepageSetting {
  id                          String    @id
  productOfTheMonthId         String?
  bannerText                  String?
  bannerCtaText               String?
  bannerCtaLink               String?
  isActive                    Boolean   @default(true)
  createdAt                   DateTime  @default(now())
  updatedAt                   DateTime  @updatedAt
  bannerBackgroundColor       String?   @default("#22c55e")
  bestsellerIds               String[]  @default([])
  newsletterSubtitle          String?   @default("Get the latest updates on new products and exclusive offers")
  newsletterTitle             String?   @default("Stay Updated")
  productSectionBgColor       String?   @default("#f0fdf4")
  heroBackgroundColor         String?   @default("#f0fdf4")
  heroCtaLink                 String?   @default("/shop")
  heroCtaText                 String?   @default("Shop Collection")
  heroSubtitle                String?   @default("Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin")
  heroTitle                   String?   @default("Natural Skincare Essentials")
  showBanner                  Boolean   @default(true)
  showBestsellers             Boolean   @default(true)
  showCategories              Boolean   @default(true)
  showHero                    Boolean   @default(true)
  showNewsletter              Boolean   @default(true)
  showProductOfMonth          Boolean   @default(true)
  showTrustBadges             Boolean   @default(true)
  flashSaleBackgroundColor    String?   @default("#16a34a")
  flashSaleEndDate            DateTime?
  flashSaleSubtitle           String?   @default("Get 25% off all natural skincare products")
  flashSaleTitle              String?   @default("Weekend Flash Sale")
  showFlashSale               Boolean   @default(true)
  showTestimonials            Boolean   @default(true)
  testimonialsBackgroundColor String?   @default("#f0fdf4")
  testimonialsSubtitle        String?   @default("Real reviews from real customers who love our natural skincare")
  testimonialsTitle           String?   @default("What Our Customers Say")
  heroBadgeText               String?   @default("New Collection")
  heroSecondaryCtaLink        String?   @default("/categories")
  heroSecondaryCtaText        String?   @default("View Categories")
  trustIndicator1Label        String?   @default("Natural")
  trustIndicator1Value        String?   @default("100%")
  trustIndicator2Label        String?   @default("Happy Customers")
  trustIndicator2Value        String?   @default("500+")
  trustIndicator3Label        String?   @default("Products")
  trustIndicator3Value        String?   @default("50+")
  trustIndicator4Label        String?   @default("Rating")
  trustIndicator4Value        String?   @default("4.8★")
  flashSalePercentage         Float?    @default(25)
  productOfTheMonth           Product?  @relation("ProductOfTheMonth", fields: [productOfTheMonthId], references: [id])

  @@map("homepage_settings")
}

model NewsletterSubscriber {
  id             String    @id @default(cuid())
  email          String    @unique
  name           String?
  isActive       Boolean   @default(true)
  source         String?   @default("homepage")
  subscribedAt   DateTime  @default(now())
  unsubscribedAt DateTime?
  whatsapp       String?

  @@map("newsletter_subscribers")
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  content   String
  rating    Int      @default(5)
  image     String?
  position  String?
  company   String?
  isActive  Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonials")
}

model Notification {
  id          String                @id @default(cuid())
  type        NotificationType
  title       String
  message     String
  data        Json?
  isRead      Boolean               @default(false)
  emailSent   Boolean               @default(false)
  emailSentAt DateTime?
  emailError  String?
  expiresAt   DateTime?
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt
  userId      String
  templateId  String?
  template    NotificationTemplate? @relation(fields: [templateId], references: [id])
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
  @@index([type, createdAt])
  @@map("notifications")
}

model NotificationTemplate {
  id            String           @id @default(cuid())
  name          String           @unique
  type          NotificationType
  title         String
  message       String
  emailSubject  String?
  emailTemplate String?
  isActive      Boolean          @default(true)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  notifications Notification[]

  @@map("notification_templates")
}

enum UserRole {
  ADMIN
  CUSTOMER
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PricingMode {
  REPLACE
  INCREMENT
  FIXED
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

enum CouponType {
  STORE_WIDE
  PRODUCT_SPECIFIC
  CATEGORY_SPECIFIC
  MINIMUM_PURCHASE
  BUNDLE_DEAL
  FIRST_TIME_CUSTOMER
  LOYALTY_REWARD
  SEASONAL
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
  FREE_SHIPPING
  BUY_X_GET_Y
}

enum NotificationType {
  ORDER_PLACED
  ORDER_CONFIRMED
  ORDER_PROCESSING
  ORDER_SHIPPED
  ORDER_DELIVERED
  ORDER_CANCELLED
  WISHLIST_ADDED
  WISHLIST_REMOVED
  PRICE_DROP_ALERT
  REVIEW_REQUEST
  REVIEW_SUBMITTED
  ADMIN_MESSAGE
  BROADCAST
  PROMOTIONAL
  SYSTEM
}
