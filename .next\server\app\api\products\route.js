"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/route.ts */ \"(rsc)/./app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/route.ts":
/*!***********************************!*\
  !*** ./app/api/products/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n\n\n// GET /api/products - List all products\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const category = searchParams.get(\"category\");\n        const search = searchParams.get(\"search\");\n        const sort = searchParams.get(\"sort\") || \"random\"; // Default to random ordering\n        const skip = (page - 1) * limit;\n        const where = {\n            isActive: true\n        };\n        // Build AND conditions array\n        const andConditions = [];\n        if (category) {\n            andConditions.push({\n                OR: [\n                    // Match products with the category as primary category\n                    {\n                        category: {\n                            slug: category\n                        }\n                    },\n                    // Match products with the category in their many-to-many relationships\n                    {\n                        productCategories: {\n                            some: {\n                                category: {\n                                    slug: category\n                                }\n                            }\n                        }\n                    }\n                ]\n            });\n        }\n        if (search) {\n            andConditions.push({\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        description: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            });\n        }\n        if (andConditions.length > 0) {\n            where.AND = andConditions;\n        }\n        // Define ordering based on sort parameter\n        let orderBy;\n        switch(sort){\n            case \"name_asc\":\n                orderBy = {\n                    name: \"asc\"\n                };\n                break;\n            case \"name_desc\":\n                orderBy = {\n                    name: \"desc\"\n                };\n                break;\n            case \"price_asc\":\n                orderBy = {\n                    price: \"asc\"\n                };\n                break;\n            case \"price_desc\":\n                orderBy = {\n                    price: \"desc\"\n                };\n                break;\n            case \"newest\":\n                orderBy = {\n                    createdAt: \"desc\"\n                };\n                break;\n            case \"oldest\":\n                orderBy = {\n                    createdAt: \"asc\"\n                };\n                break;\n            case \"random\":\n            default:\n                // For random ordering, we'll use a different approach\n                orderBy = undefined;\n                break;\n        }\n        let products;\n        let total;\n        if (sort === \"random\") {\n            // For random ordering, we need to handle it differently\n            // First get the total count\n            total = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.count({\n                where\n            });\n            // For random ordering, we'll fetch all products and shuffle them\n            // This is acceptable for small to medium datasets\n            const allProducts = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n                where,\n                include: {\n                    category: true,\n                    productCategories: {\n                        include: {\n                            category: true\n                        }\n                    },\n                    images: true,\n                    variants: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                }\n            });\n            // Shuffle the products using Fisher-Yates algorithm\n            const shuffled = [\n                ...allProducts\n            ];\n            for(let i = shuffled.length - 1; i > 0; i--){\n                const j = Math.floor(Math.random() * (i + 1));\n                [shuffled[i], shuffled[j]] = [\n                    shuffled[j],\n                    shuffled[i]\n                ];\n            }\n            // Apply pagination to shuffled results\n            products = shuffled.slice(skip, skip + limit);\n        } else {\n            // For non-random sorting, use standard Prisma query\n            [products, total] = await Promise.all([\n                _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n                    where,\n                    include: {\n                        category: true,\n                        productCategories: {\n                            include: {\n                                category: true\n                            }\n                        },\n                        images: true,\n                        variants: true,\n                        _count: {\n                            select: {\n                                reviews: true\n                            }\n                        }\n                    },\n                    orderBy,\n                    skip,\n                    take: limit\n                }),\n                _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.count({\n                    where\n                })\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: products,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch products\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/products - Create a new product\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, slug, description, shortDescription, price, comparePrice, categoryId, categoryIds = [], images, isFeatured, variations = [] } = body;\n        // Validate required fields\n        if (!name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Product name is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Allow zero or null base price - variations can provide pricing\n        let basePrice = price !== undefined ? parseFloat(price.toString()) : null;\n        // Validate that we have at least one pricing source\n        const hasValidBasePrice = basePrice !== null && basePrice >= 0;\n        const hasValidVariations = variations && variations.length > 0;\n        const warnings = [];\n        if (!hasValidBasePrice && !hasValidVariations) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Product must have either a base price (can be 0) or variations with pricing\"\n            }, {\n                status: 400\n            });\n        }\n        // Add warnings for edge cases\n        if (basePrice === 0 && (!variations || variations.length === 0)) {\n            warnings.push(\"Product has zero base price and no variations. Consider adding variations for pricing.\");\n        }\n        if (basePrice === 0 && variations && variations.length > 0) {\n            const hasZeroPricedVariations = variations.some((v)=>!v.price || v.price === 0);\n            if (hasZeroPricedVariations) {\n                warnings.push(\"Some variations have zero price. Ensure all variations have valid pricing.\");\n            }\n        }\n        // Default to 0 if no base price provided\n        if (basePrice === null) {\n            basePrice = 0;\n        }\n        // Use categoryIds if provided, otherwise fall back to single categoryId for backward compatibility\n        const categoriesToConnect = categoryIds.length > 0 ? categoryIds : categoryId ? [\n            categoryId\n        ] : [];\n        const product = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.create({\n            data: {\n                name,\n                slug,\n                description,\n                shortDescription,\n                price: basePrice,\n                comparePrice: comparePrice ? parseFloat(comparePrice.toString()) : null,\n                categoryId,\n                isFeatured: Boolean(isFeatured),\n                images: images ? {\n                    create: images.map((img, index)=>({\n                            url: img.url,\n                            alt: img.alt || name,\n                            position: index\n                        }))\n                } : undefined,\n                variants: variations.length > 0 ? {\n                    create: variations.map((variation)=>({\n                            name: variation.name,\n                            value: variation.value,\n                            price: variation.price || null,\n                            pricingMode: variation.pricingMode || \"INCREMENT\"\n                        }))\n                } : undefined,\n                productCategories: categoriesToConnect.length > 0 ? {\n                    create: categoriesToConnect.map((catId)=>({\n                            categoryId: catId\n                        }))\n                } : undefined\n            },\n            include: {\n                category: true,\n                productCategories: {\n                    include: {\n                        category: true\n                    }\n                },\n                images: true,\n                variants: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: product,\n            message: \"Product created successfully\",\n            warnings: warnings.length > 0 ? warnings : undefined\n        });\n    } catch (error) {\n        console.error(\"Error creating product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to create product\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();