"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/coupons/route";
exports.ids = ["app/api/coupons/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoupons%2Froute&page=%2Fapi%2Fcoupons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoupons%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoupons%2Froute&page=%2Fapi%2Fcoupons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoupons%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_coupons_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/coupons/route.ts */ \"(rsc)/./app/api/coupons/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/coupons/route\",\n        pathname: \"/api/coupons\",\n        filename: \"route\",\n        bundlePath: \"app/api/coupons/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\coupons\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_coupons_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/coupons/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoupons%2Froute&page=%2Fapi%2Fcoupons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoupons%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/coupons/route.ts":
/*!**********************************!*\
  !*** ./app/api/coupons/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/db */ \"(rsc)/./app/lib/db.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const isActiveParam = searchParams.get(\"active\");\n        const showInModuleParam = searchParams.get(\"showInModule\");\n        const type = searchParams.get(\"type\");\n        const userId = searchParams.get(\"userId\");\n        const skip = (page - 1) * limit;\n        const where = {};\n        // Only apply filters if explicitly requested\n        if (isActiveParam === \"true\") {\n            where.isActive = true;\n        } else if (isActiveParam === \"false\") {\n            where.isActive = false;\n        }\n        if (showInModuleParam === \"true\") {\n            where.showInModule = true;\n        } else if (showInModuleParam === \"false\") {\n            where.showInModule = false;\n        }\n        if (type) {\n            where.type = type;\n        }\n        // For regular users, only show active coupons with additional filters\n        if (!session?.user || session.user.role !== \"ADMIN\") {\n            where.isActive = true;\n            where.validFrom = {\n                lte: new Date()\n            };\n            where.OR = [\n                {\n                    validUntil: null\n                },\n                {\n                    validUntil: {\n                        gte: new Date()\n                    }\n                }\n            ];\n        }\n        // For admins, don't apply any automatic filters - they should see all coupons\n        const [coupons, total] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.coupon.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                include: {\n                    usages: userId ? {\n                        where: {\n                            userId\n                        },\n                        select: {\n                            id: true,\n                            usedAt: true\n                        }\n                    } : false\n                }\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.coupon.count({\n                where\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            coupons,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching coupons:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch coupons\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        if (!data.code || !data.name || !data.type || !data.discountType || data.discountValue === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if coupon code already exists\n        const existingCoupon = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.coupon.findUnique({\n            where: {\n                code: data.code.toUpperCase()\n            }\n        });\n        if (existingCoupon) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Coupon code already exists\"\n            }, {\n                status: 400\n            });\n        }\n        const coupon = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.coupon.create({\n            data: {\n                code: data.code.toUpperCase(),\n                name: data.name,\n                description: data.description,\n                type: data.type,\n                discountType: data.discountType,\n                discountValue: data.discountValue,\n                minimumAmount: data.minimumAmount,\n                maximumDiscount: data.maximumDiscount,\n                usageLimit: data.usageLimit,\n                userUsageLimit: data.userUsageLimit,\n                isActive: data.isActive ?? true,\n                isStackable: data.isStackable ?? false,\n                showInModule: data.showInModule ?? false,\n                validFrom: data.validFrom ? new Date(data.validFrom) : new Date(),\n                validUntil: data.validUntil ? new Date(data.validUntil) : null,\n                applicableProducts: data.applicableProducts || [],\n                applicableCategories: data.applicableCategories || [],\n                excludedProducts: data.excludedProducts || [],\n                excludedCategories: data.excludedCategories || [],\n                customerSegments: data.customerSegments || []\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(coupon, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating coupon:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create coupon\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/coupons/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_db__WEBPACK_IMPORTED_MODULE_4__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // If this is the first time the user signs in\n            if (user) {\n                token.sub = user.id;\n                token.role = user.role;\n            }\n            // For OAuth providers, ensure we get the correct user ID from database\n            if (account && token.email) {\n                try {\n                    const dbUser = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: token.email\n                        },\n                        select: {\n                            id: true,\n                            role: true\n                        }\n                    });\n                    if (dbUser) {\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                    }\n                } catch (error) {\n                    // Log error in development only\n                    if (true) {\n                        console.error(\"Error fetching user in JWT callback:\", error);\n                    }\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Always try to get fresh user data from database\n            if (token.email) {\n                try {\n                    const dbUser = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: token.email\n                        },\n                        select: {\n                            id: true,\n                            role: true,\n                            email: true,\n                            name: true\n                        }\n                    });\n                    if (dbUser) {\n                        return {\n                            ...session,\n                            user: {\n                                ...session.user,\n                                id: dbUser.id,\n                                role: dbUser.role,\n                                email: dbUser.email,\n                                name: dbUser.name\n                            }\n                        };\n                    }\n                } catch (error) {\n                    // Log error in development only\n                    if (true) {\n                        console.error(\"Error fetching user in session callback:\", error);\n                    }\n                }\n            }\n            // Fallback: if we have token.sub but no database lookup worked\n            if (session.user && token.sub) {\n                return {\n                    ...session,\n                    user: {\n                        ...session.user,\n                        id: token.sub,\n                        role: token.role\n                    }\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQzBEO0FBQ0Y7QUFDVTtBQUMvQjtBQUNMO0FBU3ZCLE1BQU1LLGNBQStCO0lBQzFDQyxTQUFTTix3RUFBYUEsQ0FBQ0ksdUNBQU1BO0lBQzdCRyxXQUFXO1FBQ1ROLHNFQUFjQSxDQUFDO1lBQ2JPLFVBQVVDLFFBQVFDLEdBQUcsQ0FBQ0MsZ0JBQWdCO1lBQ3RDQyxjQUFjSCxRQUFRQyxHQUFHLENBQUNHLG9CQUFvQjtRQUNoRDtRQUNBWCwyRUFBbUJBLENBQUM7WUFDbEJZLE1BQU07WUFDTkMsYUFBYTtnQkFDWEMsT0FBTztvQkFBRUMsT0FBTztvQkFBU0MsTUFBTTtnQkFBUTtnQkFDdkNDLFVBQVU7b0JBQUVGLE9BQU87b0JBQVlDLE1BQU07Z0JBQVc7WUFDbEQ7WUFDQSxNQUFNRSxXQUFVTCxXQUFXO2dCQUN6QixJQUFJLENBQUNBLGFBQWFDLFNBQVMsQ0FBQ0QsYUFBYUksVUFBVTtvQkFDakQsTUFBTSxJQUFJRSxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNQyxPQUFPLE1BQU1sQix1Q0FBTUEsQ0FBQ2tCLElBQUksQ0FBQ0MsVUFBVSxDQUFDO29CQUN4Q0MsT0FBTzt3QkFBRVIsT0FBT0QsWUFBWUMsS0FBSztvQkFBQztnQkFDcEM7Z0JBRUEsSUFBSSxDQUFDTSxRQUFRLENBQUNBLEtBQUtILFFBQVEsRUFBRTtvQkFDM0IsTUFBTSxJQUFJRSxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNSSxrQkFBa0IsTUFBTXRCLDZDQUFjLENBQzFDWSxZQUFZSSxRQUFRLEVBQ3BCRyxLQUFLSCxRQUFRO2dCQUdmLElBQUksQ0FBQ00saUJBQWlCO29CQUNwQixNQUFNLElBQUlKLE1BQU07Z0JBQ2xCO2dCQUVBLE9BQU87b0JBQ0xNLElBQUlMLEtBQUtLLEVBQUU7b0JBQ1hYLE9BQU9NLEtBQUtOLEtBQUs7b0JBQ2pCRixNQUFNUSxLQUFLUixJQUFJO29CQUNmYyxNQUFNTixLQUFLTSxJQUFJO2dCQUNqQjtZQUNGO1FBQ0Y7S0FDRDtJQUNEQyxTQUFTO1FBQ1BDLFVBQVU7UUFDVkMsUUFBUSxLQUFLLEtBQUssS0FBSztJQUN6QjtJQUNBQyxLQUFLO1FBQ0hELFFBQVEsS0FBSyxLQUFLLEtBQUs7SUFDekI7SUFDQUUsV0FBVztRQUNULE1BQU1ELEtBQUksRUFBRUUsS0FBSyxFQUFFWixJQUFJLEVBQUVhLE9BQU8sRUFBRTtZQUNoQyw4Q0FBOEM7WUFDOUMsSUFBSWIsTUFBTTtnQkFDUlksTUFBTUUsR0FBRyxHQUFHZCxLQUFLSyxFQUFFO2dCQUNuQk8sTUFBTU4sSUFBSSxHQUFHLEtBQXVCQSxJQUFJO1lBQzFDO1lBRUEsdUVBQXVFO1lBQ3ZFLElBQUlPLFdBQVdELE1BQU1sQixLQUFLLEVBQUU7Z0JBQzFCLElBQUk7b0JBQ0YsTUFBTXFCLFNBQVMsTUFBTWpDLHVDQUFNQSxDQUFDa0IsSUFBSSxDQUFDQyxVQUFVLENBQUM7d0JBQzFDQyxPQUFPOzRCQUFFUixPQUFPa0IsTUFBTWxCLEtBQUs7d0JBQUM7d0JBQzVCc0IsUUFBUTs0QkFBRVgsSUFBSTs0QkFBTUMsTUFBTTt3QkFBSztvQkFDakM7b0JBRUEsSUFBSVMsUUFBUTt3QkFDVkgsTUFBTUUsR0FBRyxHQUFHQyxPQUFPVixFQUFFO3dCQUNyQk8sTUFBTU4sSUFBSSxHQUFHUyxPQUFPVCxJQUFJO29CQUMxQjtnQkFDRixFQUFFLE9BQU9XLE9BQU87b0JBQ2QsZ0NBQWdDO29CQUNoQyxJQUFJOUIsSUFBeUIsRUFBZTt3QkFDMUMrQixRQUFRRCxLQUFLLENBQUMsd0NBQXdDQTtvQkFDeEQ7Z0JBQ0Y7WUFDRjtZQUVBLE9BQU9MO1FBQ1Q7UUFDQSxNQUFNTCxTQUFRLEVBQUVBLE9BQU8sRUFBRUssS0FBSyxFQUFFO1lBQzlCLGtEQUFrRDtZQUNsRCxJQUFJQSxNQUFNbEIsS0FBSyxFQUFFO2dCQUNmLElBQUk7b0JBQ0YsTUFBTXFCLFNBQVMsTUFBTWpDLHVDQUFNQSxDQUFDa0IsSUFBSSxDQUFDQyxVQUFVLENBQUM7d0JBQzFDQyxPQUFPOzRCQUFFUixPQUFPa0IsTUFBTWxCLEtBQUs7d0JBQUM7d0JBQzVCc0IsUUFBUTs0QkFBRVgsSUFBSTs0QkFBTUMsTUFBTTs0QkFBTVosT0FBTzs0QkFBTUYsTUFBTTt3QkFBSztvQkFDMUQ7b0JBRUEsSUFBSXVCLFFBQVE7d0JBQ1YsT0FBTzs0QkFDTCxHQUFHUixPQUFPOzRCQUNWUCxNQUFNO2dDQUNKLEdBQUdPLFFBQVFQLElBQUk7Z0NBQ2ZLLElBQUlVLE9BQU9WLEVBQUU7Z0NBQ2JDLE1BQU1TLE9BQU9ULElBQUk7Z0NBQ2pCWixPQUFPcUIsT0FBT3JCLEtBQUs7Z0NBQ25CRixNQUFNdUIsT0FBT3ZCLElBQUk7NEJBQ25CO3dCQUNGO29CQUNGO2dCQUNGLEVBQUUsT0FBT3lCLE9BQU87b0JBQ2QsZ0NBQWdDO29CQUNoQyxJQUFJOUIsSUFBeUIsRUFBZTt3QkFDMUMrQixRQUFRRCxLQUFLLENBQUMsNENBQTRDQTtvQkFDNUQ7Z0JBQ0Y7WUFDRjtZQUVBLCtEQUErRDtZQUMvRCxJQUFJVixRQUFRUCxJQUFJLElBQUlZLE1BQU1FLEdBQUcsRUFBRTtnQkFDN0IsT0FBTztvQkFDTCxHQUFHUCxPQUFPO29CQUNWUCxNQUFNO3dCQUNKLEdBQUdPLFFBQVFQLElBQUk7d0JBQ2ZLLElBQUlPLE1BQU1FLEdBQUc7d0JBQ2JSLE1BQU1NLE1BQU1OLElBQUk7b0JBQ2xCO2dCQUNGO1lBQ0Y7WUFFQSxPQUFPQztRQUNUO1FBQ0EsTUFBTVksVUFBUyxFQUFFQyxHQUFHLEVBQUVDLE9BQU8sRUFBRTtZQUM3QixnQ0FBZ0M7WUFDaEMsSUFBSUQsSUFBSUUsVUFBVSxDQUFDLE1BQU0sT0FBTyxDQUFDLEVBQUVELFFBQVEsRUFBRUQsSUFBSSxDQUFDO2lCQUU3QyxJQUFJLElBQUlHLElBQUlILEtBQUtJLE1BQU0sS0FBS0gsU0FBUyxPQUFPRDtZQUNqRCxPQUFPQztRQUNUO0lBQ0Y7SUFDQUksUUFBUTtRQUNOLE1BQU1DLFFBQU8sRUFBRTFCLElBQUksRUFBRWEsT0FBTyxFQUFFYyxPQUFPLEVBQUVDLFNBQVMsRUFBRTtZQUNoRCx5Q0FBeUM7WUFDekMsSUFBSXpDLElBQXlCLEVBQWU7Z0JBQzFDK0IsUUFBUVcsR0FBRyxDQUFDLGtCQUFrQjtvQkFDNUJDLFFBQVE5QixLQUFLSyxFQUFFO29CQUNmWCxPQUFPTSxLQUFLTixLQUFLO29CQUNqQnFDLFVBQVVsQixTQUFTa0I7b0JBQ25CSDtnQkFDRjtZQUNGO1FBQ0Y7SUFDRjtJQUNBSSxPQUFPO1FBQ0xOLFFBQVE7UUFDUk8sU0FBUztRQUNUaEIsT0FBTztJQUNUO0lBQ0FpQixRQUFRL0MsUUFBUUMsR0FBRyxDQUFDK0MsZUFBZTtJQUNuQ0MsT0FBT2pELGtCQUF5QjtBQUNsQyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvYXV0aC50cz82YmZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRBdXRoT3B0aW9ucyB9IGZyb20gXCJuZXh0LWF1dGhcIjtcclxuaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gXCJAbmV4dC1hdXRoL3ByaXNtYS1hZGFwdGVyXCI7XHJcbmltcG9ydCBHb29nbGVQcm92aWRlciBmcm9tIFwibmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGVcIjtcclxuaW1wb3J0IENyZWRlbnRpYWxzUHJvdmlkZXIgZnJvbSBcIm5leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcclxuaW1wb3J0ICogYXMgYmNyeXB0IGZyb20gXCJiY3J5cHRqc1wiO1xyXG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tIFwiLi9kYlwiO1xyXG5cclxuaW50ZXJmYWNlIEV4dGVuZGVkVXNlciB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIG5hbWU/OiBzdHJpbmcgfCBudWxsO1xyXG4gIHJvbGU6IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XHJcbiAgYWRhcHRlcjogUHJpc21hQWRhcHRlcihwcmlzbWEpLFxyXG4gIHByb3ZpZGVyczogW1xyXG4gICAgR29vZ2xlUHJvdmlkZXIoe1xyXG4gICAgICBjbGllbnRJZDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9JRCEsXHJcbiAgICAgIGNsaWVudFNlY3JldDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9TRUNSRVQhLFxyXG4gICAgfSksXHJcbiAgICBDcmVkZW50aWFsc1Byb3ZpZGVyKHtcclxuICAgICAgbmFtZTogXCJjcmVkZW50aWFsc1wiLFxyXG4gICAgICBjcmVkZW50aWFsczoge1xyXG4gICAgICAgIGVtYWlsOiB7IGxhYmVsOiBcIkVtYWlsXCIsIHR5cGU6IFwiZW1haWxcIiB9LFxyXG4gICAgICAgIHBhc3N3b3JkOiB7IGxhYmVsOiBcIlBhc3N3b3JkXCIsIHR5cGU6IFwicGFzc3dvcmRcIiB9XHJcbiAgICAgIH0sXHJcbiAgICAgIGFzeW5jIGF1dGhvcml6ZShjcmVkZW50aWFscykge1xyXG4gICAgICAgIGlmICghY3JlZGVudGlhbHM/LmVtYWlsIHx8ICFjcmVkZW50aWFscz8ucGFzc3dvcmQpIHtcclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgY3JlZGVudGlhbHNcIik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XHJcbiAgICAgICAgICB3aGVyZTogeyBlbWFpbDogY3JlZGVudGlhbHMuZW1haWwgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoIXVzZXIgfHwgIXVzZXIucGFzc3dvcmQpIHtcclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgY3JlZGVudGlhbHNcIik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBpc1Bhc3N3b3JkVmFsaWQgPSBhd2FpdCBiY3J5cHQuY29tcGFyZShcclxuICAgICAgICAgIGNyZWRlbnRpYWxzLnBhc3N3b3JkLFxyXG4gICAgICAgICAgdXNlci5wYXNzd29yZFxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIGlmICghaXNQYXNzd29yZFZhbGlkKSB7XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIGNyZWRlbnRpYWxzXCIpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIGlkOiB1c2VyLmlkLFxyXG4gICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgICBuYW1lOiB1c2VyLm5hbWUsXHJcbiAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgICAgfSBhcyBFeHRlbmRlZFVzZXI7XHJcbiAgICAgIH1cclxuICAgIH0pXHJcbiAgXSxcclxuICBzZXNzaW9uOiB7XHJcbiAgICBzdHJhdGVneTogXCJqd3RcIixcclxuICAgIG1heEFnZTogMzAgKiAyNCAqIDYwICogNjAsIC8vIDMwIGRheXNcclxuICB9LFxyXG4gIGp3dDoge1xyXG4gICAgbWF4QWdlOiAzMCAqIDI0ICogNjAgKiA2MCwgLy8gMzAgZGF5c1xyXG4gIH0sXHJcbiAgY2FsbGJhY2tzOiB7XHJcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgdXNlciwgYWNjb3VudCB9KSB7XHJcbiAgICAgIC8vIElmIHRoaXMgaXMgdGhlIGZpcnN0IHRpbWUgdGhlIHVzZXIgc2lnbnMgaW5cclxuICAgICAgaWYgKHVzZXIpIHtcclxuICAgICAgICB0b2tlbi5zdWIgPSB1c2VyLmlkO1xyXG4gICAgICAgIHRva2VuLnJvbGUgPSAodXNlciBhcyBFeHRlbmRlZFVzZXIpLnJvbGU7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIEZvciBPQXV0aCBwcm92aWRlcnMsIGVuc3VyZSB3ZSBnZXQgdGhlIGNvcnJlY3QgdXNlciBJRCBmcm9tIGRhdGFiYXNlXHJcbiAgICAgIGlmIChhY2NvdW50ICYmIHRva2VuLmVtYWlsKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IGRiVXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xyXG4gICAgICAgICAgICB3aGVyZTogeyBlbWFpbDogdG9rZW4uZW1haWwgfSxcclxuICAgICAgICAgICAgc2VsZWN0OiB7IGlkOiB0cnVlLCByb2xlOiB0cnVlIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpZiAoZGJVc2VyKSB7XHJcbiAgICAgICAgICAgIHRva2VuLnN1YiA9IGRiVXNlci5pZDtcclxuICAgICAgICAgICAgdG9rZW4ucm9sZSA9IGRiVXNlci5yb2xlO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAvLyBMb2cgZXJyb3IgaW4gZGV2ZWxvcG1lbnQgb25seVxyXG4gICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgaW4gSldUIGNhbGxiYWNrOicsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIHJldHVybiB0b2tlbjtcclxuICAgIH0sXHJcbiAgICBhc3luYyBzZXNzaW9uKHsgc2Vzc2lvbiwgdG9rZW4gfSkge1xyXG4gICAgICAvLyBBbHdheXMgdHJ5IHRvIGdldCBmcmVzaCB1c2VyIGRhdGEgZnJvbSBkYXRhYmFzZVxyXG4gICAgICBpZiAodG9rZW4uZW1haWwpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgZGJVc2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XHJcbiAgICAgICAgICAgIHdoZXJlOiB7IGVtYWlsOiB0b2tlbi5lbWFpbCB9LFxyXG4gICAgICAgICAgICBzZWxlY3Q6IHsgaWQ6IHRydWUsIHJvbGU6IHRydWUsIGVtYWlsOiB0cnVlLCBuYW1lOiB0cnVlIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpZiAoZGJVc2VyKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgLi4uc2Vzc2lvbixcclxuICAgICAgICAgICAgICB1c2VyOiB7XHJcbiAgICAgICAgICAgICAgICAuLi5zZXNzaW9uLnVzZXIsXHJcbiAgICAgICAgICAgICAgICBpZDogZGJVc2VyLmlkLFxyXG4gICAgICAgICAgICAgICAgcm9sZTogZGJVc2VyLnJvbGUsXHJcbiAgICAgICAgICAgICAgICBlbWFpbDogZGJVc2VyLmVtYWlsLFxyXG4gICAgICAgICAgICAgICAgbmFtZTogZGJVc2VyLm5hbWUsXHJcbiAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgLy8gTG9nIGVycm9yIGluIGRldmVsb3BtZW50IG9ubHlcclxuICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB1c2VyIGluIHNlc3Npb24gY2FsbGJhY2s6JywgZXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gRmFsbGJhY2s6IGlmIHdlIGhhdmUgdG9rZW4uc3ViIGJ1dCBubyBkYXRhYmFzZSBsb29rdXAgd29ya2VkXHJcbiAgICAgIGlmIChzZXNzaW9uLnVzZXIgJiYgdG9rZW4uc3ViKSB7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIC4uLnNlc3Npb24sXHJcbiAgICAgICAgICB1c2VyOiB7XHJcbiAgICAgICAgICAgIC4uLnNlc3Npb24udXNlcixcclxuICAgICAgICAgICAgaWQ6IHRva2VuLnN1YixcclxuICAgICAgICAgICAgcm9sZTogdG9rZW4ucm9sZSBhcyBzdHJpbmcsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH07XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIHJldHVybiBzZXNzaW9uO1xyXG4gICAgfSxcclxuICAgIGFzeW5jIHJlZGlyZWN0KHsgdXJsLCBiYXNlVXJsIH0pIHtcclxuICAgICAgLy8gQWxsb3dzIHJlbGF0aXZlIGNhbGxiYWNrIFVSTHNcclxuICAgICAgaWYgKHVybC5zdGFydHNXaXRoKFwiL1wiKSkgcmV0dXJuIGAke2Jhc2VVcmx9JHt1cmx9YDtcclxuICAgICAgLy8gQWxsb3dzIGNhbGxiYWNrIFVSTHMgb24gdGhlIHNhbWUgb3JpZ2luXHJcbiAgICAgIGVsc2UgaWYgKG5ldyBVUkwodXJsKS5vcmlnaW4gPT09IGJhc2VVcmwpIHJldHVybiB1cmw7XHJcbiAgICAgIHJldHVybiBiYXNlVXJsO1xyXG4gICAgfSxcclxuICB9LFxyXG4gIGV2ZW50czoge1xyXG4gICAgYXN5bmMgc2lnbkluKHsgdXNlciwgYWNjb3VudCwgcHJvZmlsZSwgaXNOZXdVc2VyIH0pIHtcclxuICAgICAgLy8gTG9nIHNpZ24gaW4gZXZlbnRzIGluIGRldmVsb3BtZW50IG9ubHlcclxuICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1NpZ24gaW4gZXZlbnQ6Jywge1xyXG4gICAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgICBwcm92aWRlcjogYWNjb3VudD8ucHJvdmlkZXIsXHJcbiAgICAgICAgICBpc05ld1VzZXJcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSxcclxuICB9LFxyXG4gIHBhZ2VzOiB7XHJcbiAgICBzaWduSW46IFwiL2xvZ2luXCIsXHJcbiAgICBzaWduT3V0OiBcIi9cIixcclxuICAgIGVycm9yOiBcIi9sb2dpblwiLFxyXG4gIH0sXHJcbiAgc2VjcmV0OiBwcm9jZXNzLmVudi5ORVhUQVVUSF9TRUNSRVQsXHJcbiAgZGVidWc6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnLFxyXG59OyJdLCJuYW1lcyI6WyJQcmlzbWFBZGFwdGVyIiwiR29vZ2xlUHJvdmlkZXIiLCJDcmVkZW50aWFsc1Byb3ZpZGVyIiwiYmNyeXB0IiwicHJpc21hIiwiYXV0aE9wdGlvbnMiLCJhZGFwdGVyIiwicHJvdmlkZXJzIiwiY2xpZW50SWQiLCJwcm9jZXNzIiwiZW52IiwiR09PR0xFX0NMSUVOVF9JRCIsImNsaWVudFNlY3JldCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIiwibmFtZSIsImNyZWRlbnRpYWxzIiwiZW1haWwiLCJsYWJlbCIsInR5cGUiLCJwYXNzd29yZCIsImF1dGhvcml6ZSIsIkVycm9yIiwidXNlciIsImZpbmRVbmlxdWUiLCJ3aGVyZSIsImlzUGFzc3dvcmRWYWxpZCIsImNvbXBhcmUiLCJpZCIsInJvbGUiLCJzZXNzaW9uIiwic3RyYXRlZ3kiLCJtYXhBZ2UiLCJqd3QiLCJjYWxsYmFja3MiLCJ0b2tlbiIsImFjY291bnQiLCJzdWIiLCJkYlVzZXIiLCJzZWxlY3QiLCJlcnJvciIsImNvbnNvbGUiLCJyZWRpcmVjdCIsInVybCIsImJhc2VVcmwiLCJzdGFydHNXaXRoIiwiVVJMIiwib3JpZ2luIiwiZXZlbnRzIiwic2lnbkluIiwicHJvZmlsZSIsImlzTmV3VXNlciIsImxvZyIsInVzZXJJZCIsInByb3ZpZGVyIiwicGFnZXMiLCJzaWduT3V0Iiwic2VjcmV0IiwiTkVYVEFVVEhfU0VDUkVUIiwiZGVidWciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoupons%2Froute&page=%2Fapi%2Fcoupons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoupons%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();