/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/CartContext.tsx */ \"(ssr)/./app/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/FlashSaleContext.tsx */ \"(ssr)/./app/context/FlashSaleContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/NotificationContext.tsx */ \"(ssr)/./app/context/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/SessionProvider.tsx */ \"(ssr)/./app/context/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q2FwcCU1QyU1Q2NvbnRleHQlNUMlNUNDYXJ0Q29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDYXJ0UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDYXBwJTVDJTVDY29udGV4dCU1QyU1Q0ZsYXNoU2FsZUNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRmxhc2hTYWxlUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDYXBwJTVDJTVDY29udGV4dCU1QyU1Q05vdGlmaWNhdGlvbkNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTm90aWZpY2F0aW9uUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDYXBwJTVDJTVDY29udGV4dCU1QyU1Q1Nlc3Npb25Qcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRGVza3RvcCU1QyU1Q3Byb2plY3QlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXVJO0FBQ3ZJO0FBQ0EsZ0xBQWlKO0FBQ2pKO0FBQ0Esc0xBQXVKO0FBQ3ZKO0FBQ0EsOEtBQXNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8/YTM2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNhcnRQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFxwcm9qZWN0XFxcXGFwcFxcXFxjb250ZXh0XFxcXENhcnRDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRmxhc2hTYWxlUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERlc2t0b3BcXFxccHJvamVjdFxcXFxhcHBcXFxcY29udGV4dFxcXFxGbGFzaFNhbGVDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTm90aWZpY2F0aW9uUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERlc2t0b3BcXFxccHJvamVjdFxcXFxhcHBcXFxcY29udGV4dFxcXFxOb3RpZmljYXRpb25Db250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFxwcm9qZWN0XFxcXGFwcFxcXFxjb250ZXh0XFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// localStorage utilities\nconst CART_STORAGE_KEY = \"herbalicious_cart\";\nconst saveCartToStorage = (state)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error saving cart to localStorage:\", error);\n    }\n};\nconst loadCartFromStorage = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error loading cart from localStorage:\", error);\n    }\n    return null;\n};\n// Helper function to generate unique variant key\nconst generateVariantKey = (productId, selectedVariants)=>{\n    if (!selectedVariants || selectedVariants.length === 0) {\n        return productId;\n    }\n    // Sort variants by name to ensure consistent key generation\n    const sortedVariants = [\n        ...selectedVariants\n    ].sort((a, b)=>a.name.localeCompare(b.name));\n    const variantString = sortedVariants.map((v)=>`${v.name}:${v.value}`).join(\"|\");\n    return `${productId}__${variantString}`;\n};\n// Helper function to get item identifier (with fallback for backward compatibility)\nconst getItemIdentifier = (item)=>{\n    return item.variantKey || item.product?.id || item.id;\n};\nconst getInitialCartState = ()=>{\n    const storedCart = loadCartFromStorage();\n    if (storedCart) {\n        return storedCart;\n    }\n    return {\n        items: [],\n        total: 0,\n        subtotal: 0,\n        itemCount: 0,\n        finalTotal: 0,\n        coupons: {\n            appliedCoupons: [],\n            totalDiscount: 0,\n            availableCoupons: []\n        }\n    };\n};\nconst calculateTotals = (items, appliedCoupons)=>{\n    const subtotal = items.reduce((sum, item)=>sum + item.product.price * item.quantity, 0);\n    const itemCount = items.reduce((sum, item)=>sum + item.quantity, 0);\n    const totalDiscount = appliedCoupons.reduce((sum, coupon)=>sum + coupon.discountAmount, 0);\n    const finalTotal = subtotal - totalDiscount;\n    return {\n        subtotal,\n        itemCount,\n        total: subtotal,\n        finalTotal,\n        totalDiscount\n    };\n};\nconst cartReducer = (state, action)=>{\n    let newState;\n    switch(action.type){\n        case \"ADD_ITEM\":\n            {\n                const variantKey = generateVariantKey(action.payload.id, action.selectedVariants);\n                const existingItem = state.items.find((item)=>getItemIdentifier(item) === variantKey);\n                let updatedItems;\n                if (existingItem) {\n                    // Same product with same variants - increase quantity\n                    updatedItems = state.items.map((item)=>getItemIdentifier(item) === variantKey ? {\n                            ...item,\n                            quantity: item.quantity + 1,\n                            variantKey\n                        } : item);\n                } else {\n                    // New product or different variant combination - add as new item\n                    const newCartItem = {\n                        product: action.payload,\n                        quantity: 1,\n                        selectedVariants: action.selectedVariants || [],\n                        variantKey\n                    };\n                    updatedItems = [\n                        ...state.items,\n                        newCartItem\n                    ];\n                }\n                const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: updatedItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"REMOVE_ITEM\":\n            {\n                const filteredItems = state.items.filter((item)=>getItemIdentifier(item) !== action.payload);\n                const totals = calculateTotals(filteredItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: filteredItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"UPDATE_QUANTITY\":\n            {\n                const updatedItems = state.items.map((item)=>getItemIdentifier(item) === action.payload.id ? {\n                        ...item,\n                        quantity: action.payload.quantity\n                    } : item).filter((item)=>item.quantity > 0);\n                const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: updatedItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"APPLY_COUPON\":\n            {\n                // Check if coupon is already applied\n                const isAlreadyApplied = state.coupons.appliedCoupons.some((coupon)=>coupon.coupon.id === action.payload.coupon.id);\n                if (isAlreadyApplied) {\n                    return state;\n                }\n                // Check stacking rules\n                const hasNonStackableCoupon = state.coupons.appliedCoupons.some((coupon)=>!coupon.coupon.isStackable);\n                if (hasNonStackableCoupon && !action.payload.coupon.isStackable) {\n                    return state;\n                }\n                const updatedAppliedCoupons = [\n                    ...state.coupons.appliedCoupons,\n                    action.payload\n                ];\n                const totals = calculateTotals(state.items, updatedAppliedCoupons);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"REMOVE_COUPON\":\n            {\n                const updatedAppliedCoupons = state.coupons.appliedCoupons.filter((coupon)=>coupon.coupon.id !== action.payload);\n                const totals = calculateTotals(state.items, updatedAppliedCoupons);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"CLEAR_COUPONS\":\n            {\n                const totals = calculateTotals(state.items, []);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    }\n                };\n                break;\n            }\n        case \"CLEAR_CART\":\n            {\n                newState = {\n                    items: [],\n                    total: 0,\n                    subtotal: 0,\n                    itemCount: 0,\n                    finalTotal: 0,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    }\n                };\n                break;\n            }\n        default:\n            return state;\n    }\n    // Save to localStorage after state change\n    saveCartToStorage(newState);\n    return newState;\n};\nconst CartProvider = ({ children })=>{\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(cartReducer, getInitialCartState());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            state,\n            dispatch\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\CartContext.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (!context) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlashSaleProvider: () => (/* binding */ FlashSaleProvider),\n/* harmony export */   useFlashSale: () => (/* binding */ useFlashSale)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FlashSaleProvider,useFlashSale auto */ \n\nconst FlashSaleContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction FlashSaleProvider({ children }) {\n    const [flashSaleSettings, setFlashSaleSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchFlashSaleSettings = async ()=>{\n        try {\n            const response = await fetch(\"/api/homepage-settings\");\n            const data = await response.json();\n            if (data.success && data.data.settings) {\n                const settings = data.data.settings;\n                setFlashSaleSettings({\n                    showFlashSale: settings.showFlashSale,\n                    flashSaleEndDate: settings.flashSaleEndDate,\n                    flashSalePercentage: settings.flashSalePercentage,\n                    flashSaleTitle: settings.flashSaleTitle,\n                    flashSaleSubtitle: settings.flashSaleSubtitle,\n                    flashSaleBackgroundColor: settings.flashSaleBackgroundColor\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching flash sale settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFlashSaleSettings();\n    }, []);\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await fetchFlashSaleSettings();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashSaleContext.Provider, {\n        value: {\n            flashSaleSettings,\n            loading,\n            refreshSettings\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\FlashSaleContext.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\nfunction useFlashSale() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FlashSaleContext);\n    if (context === undefined) {\n        throw new Error(\"useFlashSale must be used within a FlashSaleProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/FlashSaleContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useNotifications,NotificationProvider,default auto */ \n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n};\nconst NotificationProvider = ({ children })=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (options = {})=>{\n        if (!session?.user?.id) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const params = new URLSearchParams({\n                page: (options.page || 1).toString(),\n                limit: (options.limit || 10).toString(),\n                ...options.unreadOnly && {\n                    unreadOnly: \"true\"\n                }\n            });\n            const response = await fetch(`/api/notifications?${params}`);\n            const data = await response.json();\n            if (data.success) {\n                setNotifications(data.data.notifications);\n                setUnreadCount(data.data.unreadCount);\n            } else {\n                setError(data.error || \"Failed to fetch notifications\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching notifications:\", error);\n            setError(\"Failed to fetch notifications\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const refreshUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/unread-count\");\n            const data = await response.json();\n            if (data.success) {\n                setUnreadCount(data.unreadCount);\n            }\n        } catch (error) {\n            console.error(\"Error fetching unread count:\", error);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (notificationId)=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(`/api/notifications/${notificationId}/read`, {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                            ...notification,\n                            isRead: true\n                        } : notification));\n                // Update unread count\n                setUnreadCount((prev)=>Math.max(0, prev - 1));\n            } else {\n                setError(data.error || \"Failed to mark notification as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n            setError(\"Failed to mark notification as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAllAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/mark-all-read\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>({\n                            ...notification,\n                            isRead: true\n                        })));\n                setUnreadCount(0);\n            } else {\n                setError(data.error || \"Failed to mark all notifications as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n            setError(\"Failed to mark all notifications as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    // Fetch notifications when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"authenticated\" && session?.user?.id) {\n            fetchNotifications({\n                limit: 5\n            }); // Fetch recent notifications for dropdown\n            refreshUnreadCount();\n        }\n    }, [\n        status,\n        session?.user?.id,\n        fetchNotifications,\n        refreshUnreadCount\n    ]);\n    // Refresh unread count periodically (every 30 seconds)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!session?.user?.id) return;\n        const interval = setInterval(()=>{\n            refreshUnreadCount();\n        }, 30000); // 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        session?.user?.id,\n        refreshUnreadCount\n    ]);\n    const value = {\n        notifications,\n        unreadCount,\n        loading,\n        error,\n        fetchNotifications,\n        markAsRead,\n        markAllAsRead,\n        refreshUnreadCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\NotificationContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthSessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRDtBQU9uQyxTQUFTQyxvQkFBb0IsRUFBRUMsUUFBUSxFQUFTO0lBQzdELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2NvbnRleHQvU2Vzc2lvblByb3ZpZGVyLnRzeD82MTQxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XHJcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFByb3BzKSB7XHJcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPjtcclxufSJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/context/SessionProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ae750f5340b2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzPzIzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZTc1MGY1MzQwYjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e0),
/* harmony export */   useCart: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);


/***/ }),

/***/ "(rsc)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlashSaleProvider: () => (/* binding */ e0),
/* harmony export */   useFlashSale: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#FlashSaleProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#useFlashSale`);


/***/ }),

/***/ "(rsc)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ e1),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useNotifications: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/CartContext */ \"(rsc)/./app/context/CartContext.tsx\");\n/* harmony import */ var _context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/SessionProvider */ \"(rsc)/./app/context/SessionProvider.tsx\");\n/* harmony import */ var _context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./context/NotificationContext */ \"(rsc)/./app/context/NotificationContext.tsx\");\n/* harmony import */ var _context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context/FlashSaleContext */ \"(rsc)/./app/context/FlashSaleContext.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Herbalicious - Natural Skincare\",\n    description: \"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients.\"\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1.0,\n    themeColor: \"#16a34a\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.NotificationProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__.FlashSaleProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_CartContext__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();