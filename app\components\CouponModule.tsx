'use client'

import React, { useState, useEffect } from 'react';
import { Tag, Clock, Gift, Percent, DollarSign, AlertCircle, CheckCircle, X, Copy, Sparkles } from 'lucide-react';
import { CartItem, Coupon, AppliedCoupon, CouponValidationResult } from '../types';

interface CouponModuleProps {
  cartItems: CartItem[];
  subtotal: number;
  userId?: string;
  onCouponApply: (coupon: AppliedCoupon) => void;
  onCouponRemove: (couponId: string) => void;
  appliedCoupons: AppliedCoupon[];
}

const CouponModule: React.FC<CouponModuleProps> = ({
  cartItems,
  subtotal,
  userId,
  onCouponApply,
  onCouponRemove,
  appliedCoupons
}) => {
  const [availableCoupons, setAvailableCoupons] = useState<Coupon[]>([]);
  const [featuredCoupons, setFeaturedCoupons] = useState<Coupon[]>([]);
  const [couponCode, setCouponCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationMessage, setValidationMessage] = useState('');
  const [showAllCoupons, setShowAllCoupons] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAvailableCoupons();
    fetchFeaturedCoupons();
  }, [cartItems, userId]);

  const fetchAvailableCoupons = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        active: 'true',
        limit: '20'
      });
      
      if (userId) {
        params.append('userId', userId);
      }

      const response = await fetch(`/api/coupons?${params}`);
      if (response.ok) {
        const data = await response.json();
        const filtered = filterApplicableCoupons(data.coupons, true); // Exclude featured coupons
        setAvailableCoupons(filtered);
      }
    } catch (error) {
      console.error('Error fetching coupons:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFeaturedCoupons = async () => {
    try {
      const params = new URLSearchParams({
        active: 'true',
        showInModule: 'true',
        limit: '10'
      });
      
      if (userId) {
        params.append('userId', userId);
      }

      const response = await fetch(`/api/coupons?${params}`);
      if (response.ok) {
        const data = await response.json();
        const filtered = filterApplicableCoupons(data.coupons, false); // Don't exclude featured coupons
        setFeaturedCoupons(filtered);
      }
    } catch (error) {
      console.error('Error fetching featured coupons:', error);
    }
  };

  const filterApplicableCoupons = (coupons: Coupon[], excludeFeatured: boolean = false): Coupon[] => {
    return coupons.filter(coupon => {
      // Filter out already applied coupons
      if (appliedCoupons.some(applied => applied.coupon.id === coupon.id)) {
        return false;
      }

      // Exclude featured coupons if requested (for recommended section)
      if (excludeFeatured && coupon.showInModule) {
        return false;
      }

      // Check minimum amount
      if (coupon.minimumAmount && subtotal < coupon.minimumAmount) {
        return false;
      }

      // Check product/category applicability
      const productIds = cartItems.map(item => item.product.id);
      const categoryIds = cartItems.flatMap(item =>
        item.product.categories?.map(cat => cat.id) || []
      );

      switch (coupon.type) {
        case 'PRODUCT_SPECIFIC':
          return coupon.applicableProducts.some(id => productIds.includes(id));
        case 'CATEGORY_SPECIFIC':
          return coupon.applicableCategories.some(id => categoryIds.includes(id));
        case 'MINIMUM_PURCHASE':
          return subtotal >= (coupon.minimumAmount || 0);
        default:
          return true;
      }
    });
  };

  const validateAndApplyCoupon = async (code: string) => {
    if (!code.trim()) return;

    setIsValidating(true);
    setValidationMessage('');

    try {
      const response = await fetch('/api/coupons/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          couponCode: code,
          cartItems,
          userId,
          subtotal
        })
      });

      const result: CouponValidationResult = await response.json();

      if (result.isValid && result.coupon) {
        const appliedCoupon: AppliedCoupon = {
          coupon: result.coupon,
          discountAmount: result.discountAmount,
          isValid: true
        };
        onCouponApply(appliedCoupon);
        setCouponCode('');
        setValidationMessage('Coupon applied successfully!');
        fetchAvailableCoupons(); // Refresh available coupons
        fetchFeaturedCoupons(); // Refresh featured coupons
      } else {
        setValidationMessage(result.errorMessage || 'Invalid coupon code');
      }
    } catch (error) {
      setValidationMessage('Error validating coupon');
    } finally {
      setIsValidating(false);
    }
  };

  const applyCouponDirectly = async (coupon: Coupon) => {
    await validateAndApplyCoupon(coupon.code);
  };

  const copyCouponCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  const getCouponIcon = (type: string) => {
    switch (type) {
      case 'PERCENTAGE':
        return <Percent className="w-4 h-4" />;
      case 'FIXED_AMOUNT':
        return <DollarSign className="w-4 h-4" />;
      case 'FREE_SHIPPING':
        return <Gift className="w-4 h-4" />;
      default:
        return <Tag className="w-4 h-4" />;
    }
  };

  const getDiscountText = (coupon: Coupon) => {
    switch (coupon.discountType) {
      case 'PERCENTAGE':
        return `${coupon.discountValue}% OFF`;
      case 'FIXED_AMOUNT':
        return `₹${coupon.discountValue} OFF`;
      case 'FREE_SHIPPING':
        return 'FREE SHIPPING';
      default:
        return 'DISCOUNT';
    }
  };

  const isExpiringSoon = (coupon: Coupon) => {
    if (!coupon.validUntil) return false;
    const expiryDate = new Date(coupon.validUntil);
    const now = new Date();
    const diffTime = expiryDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 3 && diffDays > 0;
  };

  const getRecommendedCoupons = () => {
    // First try to get high-value coupons and those expiring soon
    const priorityCoupons = availableCoupons.filter(coupon => {
      const isHighValue = coupon.discountType === 'PERCENTAGE' ?
        coupon.discountValue >= 10 : // Lowered from 15 to 10
        coupon.discountValue >= 50;  // Lowered from 100 to 50
      return isHighValue || isExpiringSoon(coupon);
    });

    // If we have enough priority coupons, return them
    if (priorityCoupons.length >= 3) {
      return priorityCoupons.slice(0, 5);
    }

    // Otherwise, return the first few available coupons to ensure we always show something
    return availableCoupons.slice(0, Math.min(5, availableCoupons.length));
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const recommendedCoupons = getRecommendedCoupons();
  const displayCoupons = showAllCoupons ? availableCoupons : recommendedCoupons;

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
      <div className="flex items-center space-x-2 mb-6">
        <Sparkles className="w-5 h-5 text-green-600" />
        <h3 className="text-lg font-semibold text-gray-800">Available Coupons</h3>
      </div>

      {/* Applied Coupons */}
      {appliedCoupons.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-600 mb-3">Applied Coupons</h4>
          <div className="space-y-2">
            {appliedCoupons.map((applied) => (
              <div key={applied.coupon.id} className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <div>
                    <span className="font-medium text-green-800">{applied.coupon.code}</span>
                    <p className="text-sm text-green-600">-₹{applied.discountAmount.toFixed(2)}</p>
                  </div>
                </div>
                <button
                  onClick={() => onCouponRemove(applied.coupon.id)}
                  className="p-1 text-green-600 hover:bg-green-100 rounded-full transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Manual Coupon Entry */}
      <div className="mb-6">
        <div className="flex space-x-2">
          <input
            type="text"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
            placeholder="Enter coupon code"
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            onKeyPress={(e) => e.key === 'Enter' && validateAndApplyCoupon(couponCode)}
          />
          <button
            onClick={() => validateAndApplyCoupon(couponCode)}
            disabled={isValidating || !couponCode.trim()}
            className="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isValidating ? 'Applying...' : 'Apply'}
          </button>
        </div>
        {validationMessage && (
          <div className={`mt-2 flex items-center space-x-2 text-sm ${
            validationMessage.includes('successfully') ? 'text-green-600' : 'text-red-600'
          }`}>
            {validationMessage.includes('successfully') ? 
              <CheckCircle className="w-4 h-4" /> : 
              <AlertCircle className="w-4 h-4" />
            }
            <span>{validationMessage}</span>
          </div>
        )}
      </div>

      {/* Featured Coupons */}
      {featuredCoupons.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-600 mb-4 flex items-center space-x-2">
            <Sparkles className="w-4 h-4 text-yellow-500" />
            <span>Featured Offers</span>
          </h4>
          <div className="space-y-4">
            {featuredCoupons.map((coupon) => (
              <div
                key={coupon.id}
                className="relative bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow border-2 border-dashed border-green-400"
              >
                {/* Featured badge */}
                <div className="absolute top-3 right-3 bg-yellow-400 text-yellow-900 px-3 py-1 text-xs font-bold rounded-full z-10">
                  FEATURED
                </div>
                
                {/* Coupon content */}
                <div className="p-5 sm:p-6">
                  {/* Mobile: Stack layout, Desktop: Side by side */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div className="flex-1">
                      {/* Discount amount and icon */}
                      <div className="flex items-center gap-3 mb-2">
                        <div className="bg-green-100 text-green-700 p-2 rounded-lg">
                          {getCouponIcon(coupon.discountType)}
                        </div>
                        <div className="flex items-center flex-wrap gap-2">
                          <span className="text-2xl sm:text-3xl font-medium text-gray-900">{getDiscountText(coupon)}</span>
                          {isExpiringSoon(coupon) && (
                            <span className="px-2 py-0.5 bg-red-500 text-white text-xs rounded-full font-bold">
                              EXPIRES SOON
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {/* Coupon name */}
                      <h5 className="font-medium text-gray-900 text-base sm:text-lg mb-1">{coupon.name}</h5>
                      
                      {/* Description - hidden on very small screens */}
                      {coupon.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2 hidden sm:block">{coupon.description}</p>
                      )}
                      
                      {/* Code and validity - Compact layout */}
                      <div className="flex flex-wrap items-center gap-2 text-xs sm:text-sm">
                        <div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded font-mono">
                          <Tag className="w-3 h-3 text-gray-600" />
                          <span className="font-bold text-gray-900">{coupon.code}</span>
                          <button
                            onClick={() => copyCouponCode(coupon.code)}
                            className="p-0.5 hover:bg-gray-200 rounded"
                            title="Copy"
                          >
                            <Copy className="w-3 h-3 text-gray-600" />
                          </button>
                        </div>
                        {coupon.validUntil && (
                          <span className="text-gray-500 flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {new Date(coupon.validUntil).toLocaleDateString()}
                          </span>
                        )}
                        {coupon.minimumAmount && (
                          <span className="text-gray-500">Min: ₹{coupon.minimumAmount}</span>
                        )}
                      </div>
                    </div>
                    
                    {/* Apply button - Full width on mobile */}
                    <button
                      onClick={() => applyCouponDirectly(coupon)}
                      className="w-full sm:w-auto px-6 py-2.5 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-colors shadow hover:shadow-md"
                    >
                      APPLY
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Coupons */}
      {displayCoupons.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-600">
              {showAllCoupons ? 'All Available Coupons' : 'Recommended for You'}
            </h4>
            {availableCoupons.length > displayCoupons.length && (
              <button
                onClick={() => setShowAllCoupons(!showAllCoupons)}
                className="text-sm text-green-600 hover:text-green-700 font-medium"
              >
                {showAllCoupons ? 'Show Less' : `View All (${availableCoupons.length})`}
              </button>
            )}
          </div>

          <div className="space-y-2">
            {displayCoupons.map((coupon) => (
              <div
                key={coupon.id}
                className="relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow border border-gray-200"
              >
                {/* Dashed border effect */}
                <div className={`absolute inset-0 border border-dashed ${
                  isExpiringSoon(coupon) ? 'border-orange-300' : 'border-gray-300'
                } rounded-lg m-1`}></div>
                
                {/* Left side color bar */}
                <div className={`absolute left-0 top-0 bottom-0 w-1 sm:w-1.5 ${
                  isExpiringSoon(coupon) ? 'bg-orange-500' : 'bg-green-500'
                }`}></div>
                
                {/* Coupon content */}
                <div className="p-3 pl-4">
                  {/* Mobile: Stack layout, Desktop: Side by side */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <div className="flex-1">
                      {/* Discount and icon in one line */}
                      <div className="flex items-center gap-2 mb-1">
                        <div className={`p-1.5 rounded ${
                          isExpiringSoon(coupon) ? 'bg-orange-100 text-orange-700' : 'bg-green-100 text-green-700'
                        }`}>
                          {getCouponIcon(coupon.discountType)}
                        </div>
                        <span className="text-lg sm:text-xl font-medium text-gray-900">{getDiscountText(coupon)}</span>
                        {isExpiringSoon(coupon) && (
                          <span className="px-1.5 py-0.5 bg-orange-500 text-white text-xs rounded font-bold">
                            EXPIRES
                          </span>
                        )}
                      </div>
                      
                      {/* Name and code in compact layout */}
                      <div className="flex flex-wrap items-center gap-2 text-sm">
                        <h5 className="font-medium text-gray-800">{coupon.name}</h5>
                        <div className="flex items-center gap-1 bg-gray-100 px-2 py-0.5 rounded text-xs font-mono">
                          <span className="font-bold text-gray-900">{coupon.code}</span>
                          <button
                            onClick={() => copyCouponCode(coupon.code)}
                            className="p-0.5 hover:bg-gray-200 rounded"
                            title="Copy"
                          >
                            <Copy className="w-3 h-3 text-gray-600" />
                          </button>
                        </div>
                      </div>
                      
                      {/* Validity info - very compact */}
                      <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 mt-1">
                        {coupon.validUntil && (
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {new Date(coupon.validUntil).toLocaleDateString()}
                          </span>
                        )}
                        {coupon.minimumAmount && (
                          <span>Min: ₹{coupon.minimumAmount}</span>
                        )}
                      </div>
                    </div>
                    
                    {/* Apply button - Compact on desktop, full width on mobile */}
                    <button
                      onClick={() => applyCouponDirectly(coupon)}
                      className="w-full sm:w-auto px-4 py-2 bg-green-600 text-white text-sm rounded-lg font-semibold hover:bg-green-700 transition-colors"
                    >
                      APPLY
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {availableCoupons.length === 0 && !loading && (
        <div className="text-center py-8">
          <Gift className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">No coupons available for your current cart</p>
        </div>
      )}
    </div>
  );
};

export default CouponModule;