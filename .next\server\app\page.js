/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5Cpages%5C%5CHome.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5Cpages%5C%5CHome.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Layout.tsx */ \"(ssr)/./app/components/Layout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/pages/Home.tsx */ \"(ssr)/./app/components/pages/Home.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q2FwcCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRGVza3RvcCU1QyU1Q3Byb2plY3QlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDcGFnZXMlNUMlNUNIb21lLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFnSTtBQUNoSTtBQUNBLDBLQUFxSSIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvPzQzNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHByb2plY3RcXFxcYXBwXFxcXGNvbXBvbmVudHNcXFxcTGF5b3V0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFxwcm9qZWN0XFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXHBhZ2VzXFxcXEhvbWUudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5Cpages%5C%5CHome.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/CartContext.tsx */ \"(ssr)/./app/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/FlashSaleContext.tsx */ \"(ssr)/./app/context/FlashSaleContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/NotificationContext.tsx */ \"(ssr)/./app/context/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/SessionProvider.tsx */ \"(ssr)/./app/context/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/CountdownBanner.tsx":
/*!********************************************!*\
  !*** ./app/components/CountdownBanner.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CountdownBanner = ({ endDate, title = \"Limited Time Offer\", subtitle = \"Get 20% off all natural skincare products\", backgroundColor = \"#16a34a\" })=>{\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        days: 0,\n        hours: 0,\n        minutes: 0,\n        seconds: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Set default end date to 7 days from now if not provided\n        const targetDate = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);\n        const timer = setInterval(()=>{\n            const now = new Date().getTime();\n            const distance = targetDate.getTime() - now;\n            if (distance > 0) {\n                setTimeLeft({\n                    days: Math.floor(distance / (1000 * 60 * 60 * 24)),\n                    hours: Math.floor(distance % (1000 * 60 * 60 * 24) / (1000 * 60 * 60)),\n                    minutes: Math.floor(distance % (1000 * 60 * 60) / (1000 * 60)),\n                    seconds: Math.floor(distance % (1000 * 60) / 1000)\n                });\n            } else {\n                setTimeLeft({\n                    days: 0,\n                    hours: 0,\n                    minutes: 0,\n                    seconds: 0\n                });\n            }\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, [\n        endDate\n    ]);\n    const isExpired = timeLeft.days === 0 && timeLeft.hours === 0 && timeLeft.minutes === 0 && timeLeft.seconds === 0;\n    if (isExpired) {\n        return null; // Don't show expired countdown\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-4 mt-4 rounded-2xl p-6 text-white shadow-lg lg:mx-8 lg:mt-6 lg:p-8\",\n        style: {\n            backgroundColor\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-6 h-6 mr-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-semibold uppercase tracking-wide lg:text-base\",\n                        children: \"Flash Sale\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold mb-2 lg:text-2xl\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm opacity-90 lg:text-base\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center space-x-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-5 h-5 opacity-80\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium lg:text-base\",\n                        children: \"Ends in:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-4 gap-3 max-w-sm mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold lg:text-3xl\",\n                                children: timeLeft.days\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 lg:text-sm\",\n                                children: \"Days\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold lg:text-3xl\",\n                                children: timeLeft.hours\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 lg:text-sm\",\n                                children: \"Hours\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold lg:text-3xl\",\n                                children: timeLeft.minutes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 lg:text-sm\",\n                                children: \"Min\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold lg:text-3xl\",\n                                children: timeLeft.seconds\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 lg:text-sm\",\n                                children: \"Sec\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"bg-white text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg\",\n                    children: \"Shop Now & Save 20%\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\CountdownBanner.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountdownBanner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/CountdownBanner.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Hero.tsx":
/*!*********************************!*\
  !*** ./app/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Hero = ({ title = \"Natural Skincare Essentials\", subtitle = \"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin\", ctaText = \"Shop Collection\", ctaLink = \"/shop\", secondaryCtaText = \"View Categories\", secondaryCtaLink = \"/categories\", badgeText = \"New Collection\", backgroundColor = \"#f0fdf4\", trustIndicators = {\n    value1: \"100%\",\n    label1: \"Natural\",\n    value2: \"5K+\",\n    label2: \"Happy Lifetime Customers\",\n    value3: \"25+\",\n    label3: \"Products\",\n    value4: \"4.8★\",\n    label4: \"Rating\"\n} })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative px-4 py-12 lg:px-8 lg:py-16\",\n        style: {\n            backgroundColor\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        badgeText\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-tight\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed\",\n                    children: subtitle\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: ctaLink,\n                            className: \"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors duration-200 shadow-lg hover:shadow-xl\",\n                            children: [\n                                ctaText,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"ml-2 w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: secondaryCtaLink,\n                            className: \"inline-flex items-center px-6 py-3 border-2 border-green-600 text-green-600 font-semibold rounded-full hover:bg-green-50 transition-colors duration-200\",\n                            children: secondaryCtaText\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: trustIndicators.value1\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: trustIndicators.label1\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: trustIndicators.value2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: trustIndicators.label2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: trustIndicators.value3\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: trustIndicators.label3\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: trustIndicators.value4\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: trustIndicators.label4\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Hero.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Layout.tsx":
/*!***********************************!*\
  !*** ./app/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/CartContext */ \"(ssr)/./app/context/CartContext.tsx\");\n/* harmony import */ var _context_NotificationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/NotificationContext */ \"(ssr)/./app/context/NotificationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst Layout = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const { state } = (0,_context_CartContext__WEBPACK_IMPORTED_MODULE_6__.useCart)();\n    const { unreadCount } = (0,_context_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotifications)();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    const isActive = (path)=>pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm sticky top-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-full hover:bg-gray-100 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo.svg\",\n                                        alt: \"Herbalicious Logo\",\n                                        width: 60,\n                                        height: 60,\n                                        className: \"h-[40px] w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/notifications\",\n                                    className: \"p-2 rounded-full hover:bg-gray-100 transition-colors relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isClient && session?.user && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                            children: unreadCount > 99 ? \"99+\" : unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex px-4 py-3 items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo.svg\",\n                                        alt: \"Herbalicious Logo\",\n                                        width: 60,\n                                        height: 60,\n                                        className: \"h-[60px] w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/shop\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"Shop\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/about\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/notifications\",\n                                            className: \"p-2 rounded-full hover:bg-gray-100 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                isClient && session?.user && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: unreadCount > 99 ? \"99+\" : unreadCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/cart\",\n                                            className: \"relative p-2 rounded-full hover:bg-gray-100 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isClient && state.itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: state.itemCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/profile\",\n                                            className: \"p-2 rounded-full hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 pb-20 lg:pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto lg:max-w-none\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md mx-auto px-4 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-around\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${isActive(\"/\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/shop\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${isActive(\"/shop\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Shop\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ${isActive(\"/cart\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isClient && state.itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                        children: state.itemCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Cart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${isActive(\"/profile\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDdEI7QUFDRTtBQUNlO0FBQ0Q7QUFDb0M7QUFDaEM7QUFDaUI7QUFNbEUsTUFBTWUsU0FBZ0MsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDakQsTUFBTUMsV0FBV1osNERBQVdBO0lBQzVCLE1BQU0sRUFBRWEsTUFBTUMsT0FBTyxFQUFFLEdBQUdiLDJEQUFVQTtJQUNwQyxNQUFNLEVBQUVjLEtBQUssRUFBRSxHQUFHUCw2REFBT0E7SUFDekIsTUFBTSxFQUFFUSxXQUFXLEVBQUUsR0FBR1AsOEVBQWdCQTtJQUN4QyxNQUFNLENBQUNRLFVBQVVDLFlBQVksR0FBR3RCLCtDQUFRQSxDQUFDO0lBRXpDQyxnREFBU0EsQ0FBQztRQUNScUIsWUFBWTtJQUNkLEdBQUcsRUFBRTtJQUVMLE1BQU1DLFdBQVcsQ0FBQ0MsT0FBaUJSLGFBQWFRO0lBR2hELHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQU9ELFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQU9GLFdBQVU7OENBQ2hCLDRFQUFDeEIsaURBQUlBO3dDQUFDMkIsTUFBSztrREFDVCw0RUFBQ2xCLHdIQUFJQTs0Q0FBQ2UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJcEIsOERBQUN4QixpREFBSUE7b0NBQUMyQixNQUFLO29DQUFJSCxXQUFVOzhDQUN2Qiw0RUFBQ3ZCLGtEQUFLQTt3Q0FDSjJCLEtBQUk7d0NBQ0pDLEtBQUk7d0NBQ0pDLE9BQU87d0NBQ1BDLFFBQVE7d0NBQ1JQLFdBQVU7Ozs7Ozs7Ozs7OzhDQUlkLDhEQUFDeEIsaURBQUlBO29DQUFDMkIsTUFBSztvQ0FBaUJILFdBQVU7O3NEQUNwQyw4REFBQ2hCLHdIQUFJQTs0Q0FBQ2dCLFdBQVU7Ozs7Ozt3Q0FDZkwsWUFBWUgsU0FBU2dCLFFBQVFkLGNBQWMsbUJBQzFDLDhEQUFDZTs0Q0FBS1QsV0FBVTtzREFDYk4sY0FBYyxLQUFLLFFBQVFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3BDLDhEQUFDSzs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUN4QixpREFBSUE7b0NBQUMyQixNQUFLO29DQUFJSCxXQUFVOzhDQUN2Qiw0RUFBQ3ZCLGtEQUFLQTt3Q0FDSjJCLEtBQUk7d0NBQ0pDLEtBQUk7d0NBQ0pDLE9BQU87d0NBQ1BDLFFBQVE7d0NBQ1JQLFdBQVU7Ozs7Ozs7Ozs7OzhDQUtkLDhEQUFDVTtvQ0FBSVYsV0FBVTs7c0RBQ2IsOERBQUN4QixpREFBSUE7NENBQUMyQixNQUFLOzRDQUFJSCxXQUFVO3NEQUFtRTs7Ozs7O3NEQUc1Riw4REFBQ3hCLGlEQUFJQTs0Q0FBQzJCLE1BQUs7NENBQVFILFdBQVU7c0RBQW1FOzs7Ozs7c0RBR2hHLDhEQUFDeEIsaURBQUlBOzRDQUFDMkIsTUFBSzs0Q0FBU0gsV0FBVTtzREFBbUU7Ozs7OztzREFHakcsOERBQUN4QixpREFBSUE7NENBQUMyQixNQUFLOzRDQUFXSCxXQUFVO3NEQUFtRTs7Ozs7Ozs7Ozs7OzhDQU1yRyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNmLDhEQUFDeEIsaURBQUlBOzRDQUFDMkIsTUFBSzs0Q0FBaUJILFdBQVU7OzhEQUNwQyw4REFBQ2hCLHdIQUFJQTtvREFBQ2dCLFdBQVU7Ozs7OztnREFDZkwsWUFBWUgsU0FBU2dCLFFBQVFkLGNBQWMsbUJBQzFDLDhEQUFDZTtvREFBS1QsV0FBVTs4REFDYk4sY0FBYyxLQUFLLFFBQVFBOzs7Ozs7Ozs7Ozs7c0RBSWhDLDhEQUFDbEIsaURBQUlBOzRDQUFDMkIsTUFBSzs0Q0FBUUgsV0FBVTs7OERBQzNCLDhEQUFDbEIseUhBQVlBO29EQUFDa0IsV0FBVTs7Ozs7O2dEQUN2QkwsWUFBWUYsTUFBTWtCLFNBQVMsR0FBRyxtQkFDN0IsOERBQUNGO29EQUFLVCxXQUFVOzhEQUNiUCxNQUFNa0IsU0FBUzs7Ozs7Ozs7Ozs7O3NEQUl0Qiw4REFBQ25DLGlEQUFJQTs0Q0FBQzJCLE1BQUs7NENBQVdILFdBQVU7c0RBQzlCLDRFQUFDakIseUhBQUlBO2dEQUFDaUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRMUIsOERBQUNZO2dCQUFLWixXQUFVOzBCQUNkLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pYOzs7Ozs7Ozs7Ozs7Ozs7OzBCQU1QLDhEQUFDcUI7Z0JBQUlWLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUN4QixpREFBSUE7Z0NBQ0gyQixNQUFLO2dDQUNMSCxXQUFXLENBQUMsa0VBQWtFLEVBQzVFSCxTQUFTLE9BQ0wsK0JBQ0Esb0NBQ0wsQ0FBQzs7a0RBRUYsOERBQUNqQix5SEFBSUE7d0NBQUNvQixXQUFVOzs7Ozs7a0RBQ2hCLDhEQUFDUzt3Q0FBS1QsV0FBVTtrREFBc0I7Ozs7Ozs7Ozs7OzswQ0FHeEMsOERBQUN4QixpREFBSUE7Z0NBQ0gyQixNQUFLO2dDQUNMSCxXQUFXLENBQUMsa0VBQWtFLEVBQzVFSCxTQUFTLFdBQ0wsK0JBQ0Esb0NBQ0wsQ0FBQzs7a0RBRUYsOERBQUNoQix5SEFBV0E7d0NBQUNtQixXQUFVOzs7Ozs7a0RBQ3ZCLDhEQUFDUzt3Q0FBS1QsV0FBVTtrREFBc0I7Ozs7Ozs7Ozs7OzswQ0FHeEMsOERBQUN4QixpREFBSUE7Z0NBQ0gyQixNQUFLO2dDQUNMSCxXQUFXLENBQUMsMkVBQTJFLEVBQ3JGSCxTQUFTLFdBQ0wsK0JBQ0Esb0NBQ0wsQ0FBQzs7a0RBRUYsOERBQUNmLHlIQUFZQTt3Q0FBQ2tCLFdBQVU7Ozs7OztvQ0FDdkJMLFlBQVlGLE1BQU1rQixTQUFTLEdBQUcsbUJBQzdCLDhEQUFDRjt3Q0FBS1QsV0FBVTtrREFDYlAsTUFBTWtCLFNBQVM7Ozs7OztrREFHcEIsOERBQUNGO3dDQUFLVCxXQUFVO2tEQUFzQjs7Ozs7Ozs7Ozs7OzBDQUd4Qyw4REFBQ3hCLGlEQUFJQTtnQ0FDSDJCLE1BQUs7Z0NBQ0xILFdBQVcsQ0FBQyxrRUFBa0UsRUFDNUVILFNBQVMsY0FDTCwrQkFDQSxvQ0FDTCxDQUFDOztrREFFRiw4REFBQ2QseUhBQUlBO3dDQUFDaUIsV0FBVTs7Ozs7O2tEQUNoQiw4REFBQ1M7d0NBQUtULFdBQVU7a0RBQXNCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3BEO0FBRUEsaUVBQWVaLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2NvbXBvbmVudHMvTGF5b3V0LnRzeD8zOWI4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IEhvbWUsIFNob3BwaW5nQmFnLCBTaG9wcGluZ0NhcnQsIFVzZXIsIEJlbGwsIE1lbnUgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlQ2FydCB9IGZyb20gJy4uL2NvbnRleHQvQ2FydENvbnRleHQnO1xuaW1wb3J0IHsgdXNlTm90aWZpY2F0aW9ucyB9IGZyb20gJy4uL2NvbnRleHQvTm90aWZpY2F0aW9uQ29udGV4dCc7XG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmNvbnN0IExheW91dDogUmVhY3QuRkM8TGF5b3V0UHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCB7IHN0YXRlIH0gPSB1c2VDYXJ0KCk7XG4gIGNvbnN0IHsgdW5yZWFkQ291bnQgfSA9IHVzZU5vdGlmaWNhdGlvbnMoKTtcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRJc0NsaWVudCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGlzQWN0aXZlID0gKHBhdGg6IHN0cmluZykgPT4gcGF0aG5hbWUgPT09IHBhdGg7XG5cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgey8qIFRvcCBOYXZpZ2F0aW9uICovfVxuICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctc20gc3RpY2t5IHRvcC0wIHotNDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBsZzpweC04XCI+XG4gICAgICAgICAgey8qIE1vYmlsZSBIZWFkZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvIGxnOmhpZGRlbiBweC00IHB5LTMgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9zZXR0aW5nc1wiPlxuICAgICAgICAgICAgICAgIDxNZW51IGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgIHNyYz1cIi9sb2dvLnN2Z1wiXG4gICAgICAgICAgICAgICAgYWx0PVwiSGVyYmFsaWNpb3VzIExvZ29cIlxuICAgICAgICAgICAgICAgIHdpZHRoPXs2MH1cbiAgICAgICAgICAgICAgICBoZWlnaHQ9ezYwfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtWzQwcHhdIHctYXV0b1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbm90aWZpY2F0aW9uc1wiIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyYXktNjAwXCIgLz5cbiAgICAgICAgICAgICAge2lzQ2xpZW50ICYmIHNlc3Npb24/LnVzZXIgJiYgdW5yZWFkQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIHctNSBoLTUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIHt1bnJlYWRDb3VudCA+IDk5ID8gJzk5KycgOiB1bnJlYWRDb3VudH1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGVza3RvcCBIZWFkZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBweC00IHB5LTMgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgey8qIExvZ28gLSBMZWZ0ICovfVxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICBzcmM9XCIvbG9nby5zdmdcIlxuICAgICAgICAgICAgICAgIGFsdD1cIkhlcmJhbGljaW91cyBMb2dvXCJcbiAgICAgICAgICAgICAgICB3aWR0aD17NjB9XG4gICAgICAgICAgICAgICAgaGVpZ2h0PXs2MH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLVs2MHB4XSB3LWF1dG9cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogTmF2aWdhdGlvbiAtIENlbnRlciAqL31cbiAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC04IGFic29sdXRlIGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yXCI+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyZWVuLTYwMCBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIEhvbWVcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3Nob3BcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgU2hvcFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgQWJvdXRcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2NvbnRhY3RcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgQ29udGFjdFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIEFjdGlvbnMgLSBSaWdodCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL25vdGlmaWNhdGlvbnNcIiBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICAgIHtpc0NsaWVudCAmJiBzZXNzaW9uPy51c2VyICYmIHVucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGJnLXJlZC01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtZnVsbCB3LTUgaC01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICB7dW5yZWFkQ291bnQgPiA5OSA/ICc5OSsnIDogdW5yZWFkQ291bnR9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2NhcnRcIiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTIgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgPFNob3BwaW5nQ2FydCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIHtpc0NsaWVudCAmJiBzdGF0ZS5pdGVtQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMSAtcmlnaHQtMSBiZy1yZWQtNTAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkLWZ1bGwgdy01IGgtNSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICB7c3RhdGUuaXRlbUNvdW50fVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcm9maWxlXCIgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvaGVhZGVyPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIHBiLTIwIGxnOnBiLThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvIGxnOm1heC13LW5vbmVcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG5cbiAgICAgIHsvKiBCb3R0b20gTmF2aWdhdGlvbiAtIE1vYmlsZSBPbmx5ICovfVxuICAgICAgPG5hdiBjbGFzc05hbWU9XCJmaXhlZCBib3R0b20tMCBsZWZ0LTAgcmlnaHQtMCBiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgei01MCBsZzpoaWRkZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvIHB4LTQgcHktMlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWFyb3VuZFwiPlxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBweS0yIHB4LTMgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGlzQWN0aXZlKCcvJykgXG4gICAgICAgICAgICAgICAgICA/ICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi01MCcgXG4gICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8SG9tZSBjbGFzc05hbWU9XCJ3LTYgaC02IG1iLTFcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtXCI+SG9tZTwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9zaG9wXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgcHktMiBweC0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBpc0FjdGl2ZSgnL3Nob3AnKSBcbiAgICAgICAgICAgICAgICAgID8gJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTUwJyBcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxTaG9wcGluZ0JhZyBjbGFzc05hbWU9XCJ3LTYgaC02IG1iLTFcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtXCI+U2hvcDwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9jYXJ0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgcHktMiBweC0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgcmVsYXRpdmUgJHtcbiAgICAgICAgICAgICAgICBpc0FjdGl2ZSgnL2NhcnQnKVxuICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAnXG4gICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8U2hvcHBpbmdDYXJ0IGNsYXNzTmFtZT1cInctNiBoLTYgbWItMVwiIC8+XG4gICAgICAgICAgICAgIHtpc0NsaWVudCAmJiBzdGF0ZS5pdGVtQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIHctNSBoLTUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIHtzdGF0ZS5pdGVtQ291bnR9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtXCI+Q2FydDwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9wcm9maWxlXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgcHktMiBweC0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBpc0FjdGl2ZSgnL3Byb2ZpbGUnKSBcbiAgICAgICAgICAgICAgICAgID8gJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTUwJyBcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cInctNiBoLTYgbWItMVwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW1cIj5Qcm9maWxlPC9zcGFuPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbmF2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTGF5b3V0OyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsIkltYWdlIiwidXNlUGF0aG5hbWUiLCJ1c2VTZXNzaW9uIiwiSG9tZSIsIlNob3BwaW5nQmFnIiwiU2hvcHBpbmdDYXJ0IiwiVXNlciIsIkJlbGwiLCJNZW51IiwidXNlQ2FydCIsInVzZU5vdGlmaWNhdGlvbnMiLCJMYXlvdXQiLCJjaGlsZHJlbiIsInBhdGhuYW1lIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0ZSIsInVucmVhZENvdW50IiwiaXNDbGllbnQiLCJzZXRJc0NsaWVudCIsImlzQWN0aXZlIiwicGF0aCIsImRpdiIsImNsYXNzTmFtZSIsImhlYWRlciIsImJ1dHRvbiIsImhyZWYiLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsInVzZXIiLCJzcGFuIiwibmF2IiwiaXRlbUNvdW50IiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/NewsletterSignup.tsx":
/*!*********************************************!*\
  !*** ./app/components/NewsletterSignup.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Mail,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Mail,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Mail,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Mail,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst NewsletterSignup = ({ title = \"Stay Updated\", subtitle = \"Get the latest updates on new products and exclusive offers\", backgroundColor = \"#f0fdf4\", className = \"\", source = \"homepage\" })=>{\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [whatsapp, setWhatsapp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            setStatus(\"error\");\n            setMessage(\"Please enter your email address\");\n            return;\n        }\n        setIsSubmitting(true);\n        setStatus(\"idle\");\n        try {\n            const response = await fetch(\"/api/newsletter\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    name: name || undefined,\n                    whatsapp: whatsapp || undefined,\n                    source\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setStatus(\"success\");\n                setMessage(data.message || \"Successfully subscribed to newsletter!\");\n                setEmail(\"\");\n                setName(\"\");\n            } else {\n                setStatus(\"error\");\n                setMessage(data.error || \"Failed to subscribe. Please try again.\");\n            }\n        } catch (error) {\n            setStatus(\"error\");\n            setMessage(\"Network error. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-2xl p-6 lg:p-8 ${className}`,\n        style: {\n            backgroundColor\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xl lg:text-2xl font-bold text-gray-900 mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6 text-sm lg:text-base\",\n                    children: subtitle\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Your name (optional)\",\n                                    value: name,\n                                    onChange: (e)=>setName(e.target.value),\n                                    className: \"w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    placeholder: \"WhatsApp (optional)\",\n                                    value: whatsapp,\n                                    onChange: (e)=>setWhatsapp(e.target.value),\n                                    className: \"w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    placeholder: \"Enter your email address\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value),\n                                    required: true,\n                                    className: \"flex-1 px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting || status === \"success\",\n                                    className: `px-6 py-2.5 rounded-lg font-semibold text-white transition-all duration-200 text-sm whitespace-nowrap ${status === \"success\" ? \"bg-green-600 cursor-default\" : isSubmitting ? \"bg-green-400 cursor-not-allowed\" : \"bg-green-600 hover:bg-green-700 active:bg-green-800\"}`,\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Subscribing...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined) : status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Subscribed!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Subscribe\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `mt-4 p-3 rounded-lg flex items-center justify-center text-sm ${status === \"success\" ? \"bg-green-100 text-green-800 border border-green-200\" : \"bg-red-100 text-red-800 border border-red-200\"}`,\n                    children: [\n                        status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Mail_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 15\n                        }, undefined),\n                        message\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500 mt-4\",\n                    children: \"We respect your privacy. Unsubscribe at any time.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\NewsletterSignup.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewsletterSignup);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9OZXdzbGV0dGVyU2lnbnVwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQzBCO0FBVWxFLE1BQU1NLG1CQUFvRCxDQUFDLEVBQ3pEQyxRQUFRLGNBQWMsRUFDdEJDLFdBQVcsNkRBQTZELEVBQ3hFQyxrQkFBa0IsU0FBUyxFQUMzQkMsWUFBWSxFQUFFLEVBQ2RDLFNBQVMsVUFBVSxFQUNwQjtJQUNDLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHWiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNhLE1BQU1DLFFBQVEsR0FBR2QsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDZSxVQUFVQyxZQUFZLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpQixjQUFjQyxnQkFBZ0IsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ21CLFFBQVFDLFVBQVUsR0FBR3BCLCtDQUFRQSxDQUErQjtJQUNuRSxNQUFNLENBQUNxQixTQUFTQyxXQUFXLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNdUIsZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUNkLE9BQU87WUFDVlMsVUFBVTtZQUNWRSxXQUFXO1lBQ1g7UUFDRjtRQUVBSixnQkFBZ0I7UUFDaEJFLFVBQVU7UUFFVixJQUFJO1lBQ0YsTUFBTU0sV0FBVyxNQUFNQyxNQUFNLG1CQUFtQjtnQkFDOUNDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQnJCO29CQUNBRSxNQUFNQSxRQUFRb0I7b0JBQ2RsQixVQUFVQSxZQUFZa0I7b0JBQ3RCdkI7Z0JBQ0Y7WUFDRjtZQUVBLE1BQU13QixPQUFPLE1BQU1SLFNBQVNTLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQmhCLFVBQVU7Z0JBQ1ZFLFdBQVdZLEtBQUtiLE9BQU8sSUFBSTtnQkFDM0JULFNBQVM7Z0JBQ1RFLFFBQVE7WUFDVixPQUFPO2dCQUNMTSxVQUFVO2dCQUNWRSxXQUFXWSxLQUFLRyxLQUFLLElBQUk7WUFDM0I7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZGpCLFVBQVU7WUFDVkUsV0FBVztRQUNiLFNBQVU7WUFDUkosZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ29CO1FBQ0M3QixXQUFXLENBQUMsdUJBQXVCLEVBQUVBLFVBQVUsQ0FBQztRQUNoRDhCLE9BQU87WUFBRS9CO1FBQWdCO2tCQUV6Qiw0RUFBQzhCO1lBQUk3QixXQUFVOzs4QkFFYiw4REFBQzZCO29CQUFJN0IsV0FBVTs4QkFDYiw0RUFBQ1IsMkdBQUlBO3dCQUFDUSxXQUFVOzs7Ozs7Ozs7Ozs4QkFJbEIsOERBQUMrQjtvQkFBRy9CLFdBQVU7OEJBQ1hIOzs7Ozs7OEJBRUgsOERBQUNtQztvQkFBRWhDLFdBQVU7OEJBQ1ZGOzs7Ozs7OEJBSUgsOERBQUNtQztvQkFBS0MsVUFBVXBCO29CQUFjZCxXQUFVOztzQ0FFdEMsOERBQUM2Qjs0QkFBSTdCLFdBQVU7OzhDQUNiLDhEQUFDbUM7b0NBQ0NDLE1BQUs7b0NBQ0xDLGFBQVk7b0NBQ1pDLE9BQU9sQztvQ0FDUG1DLFVBQVUsQ0FBQ3hCLElBQU1WLFFBQVFVLEVBQUV5QixNQUFNLENBQUNGLEtBQUs7b0NBQ3ZDdEMsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDbUM7b0NBQ0NDLE1BQUs7b0NBQ0xDLGFBQVk7b0NBQ1pDLE9BQU9oQztvQ0FDUGlDLFVBQVUsQ0FBQ3hCLElBQU1SLFlBQVlRLEVBQUV5QixNQUFNLENBQUNGLEtBQUs7b0NBQzNDdEMsV0FBVTs7Ozs7Ozs7Ozs7O3NDQUtkLDhEQUFDNkI7NEJBQUk3QixXQUFVOzs4Q0FDYiw4REFBQ21DO29DQUNDQyxNQUFLO29DQUNMQyxhQUFZO29DQUNaQyxPQUFPcEM7b0NBQ1BxQyxVQUFVLENBQUN4QixJQUFNWixTQUFTWSxFQUFFeUIsTUFBTSxDQUFDRixLQUFLO29DQUN4Q0csUUFBUTtvQ0FDUnpDLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQzBDO29DQUNDTixNQUFLO29DQUNMTyxVQUFVbkMsZ0JBQWdCRSxXQUFXO29DQUNyQ1YsV0FBVyxDQUFDLHNHQUFzRyxFQUNoSFUsV0FBVyxZQUNQLGdDQUNBRixlQUNBLG9DQUNBLHNEQUNMLENBQUM7OENBRURBLDZCQUNDLDhEQUFDcUI7d0NBQUk3QixXQUFVOzswREFDYiw4REFBQzZCO2dEQUFJN0IsV0FBVTs7Ozs7OzRDQUEwRjs7Ozs7O29EQUd6R1UsV0FBVywwQkFDYiw4REFBQ21CO3dDQUFJN0IsV0FBVTs7MERBQ2IsOERBQUNQLDJHQUFLQTtnREFBQ08sV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7O2tFQUlwQyw4REFBQzZCO3dDQUFJN0IsV0FBVTs7MERBQ2IsOERBQUNMLDJHQUFRQTtnREFBQ0ssV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVM5Q1kseUJBQ0MsOERBQUNpQjtvQkFBSTdCLFdBQVcsQ0FBQyw2REFBNkQsRUFDNUVVLFdBQVcsWUFDUCx3REFDQSxnREFDTCxDQUFDOzt3QkFDQ0EsV0FBVywwQkFDViw4REFBQ2pCLDJHQUFLQTs0QkFBQ08sV0FBVTs7Ozs7c0RBRWpCLDhEQUFDTiwyR0FBV0E7NEJBQUNNLFdBQVU7Ozs7Ozt3QkFFeEJZOzs7Ozs7OzhCQUtMLDhEQUFDb0I7b0JBQUVoQyxXQUFVOzhCQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNbEQ7QUFFQSxpRUFBZUosZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9jb21wb25lbnRzL05ld3NsZXR0ZXJTaWdudXAudHN4PzQzOTUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBNYWlsLCBDaGVjaywgQWxlcnRDaXJjbGUsIFNwYXJrbGVzIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuaW50ZXJmYWNlIE5ld3NsZXR0ZXJTaWdudXBQcm9wcyB7XG4gIHRpdGxlPzogc3RyaW5nO1xuICBzdWJ0aXRsZT86IHN0cmluZztcbiAgYmFja2dyb3VuZENvbG9yPzogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHNvdXJjZT86IHN0cmluZztcbn1cblxuY29uc3QgTmV3c2xldHRlclNpZ251cDogUmVhY3QuRkM8TmV3c2xldHRlclNpZ251cFByb3BzPiA9ICh7XG4gIHRpdGxlID0gXCJTdGF5IFVwZGF0ZWRcIixcbiAgc3VidGl0bGUgPSBcIkdldCB0aGUgbGF0ZXN0IHVwZGF0ZXMgb24gbmV3IHByb2R1Y3RzIGFuZCBleGNsdXNpdmUgb2ZmZXJzXCIsXG4gIGJhY2tncm91bmRDb2xvciA9IFwiI2YwZmRmNFwiLCAvLyBncmVlbi01MFxuICBjbGFzc05hbWUgPSBcIlwiLFxuICBzb3VyY2UgPSBcImhvbWVwYWdlXCJcbn0pID0+IHtcbiAgY29uc3QgW2VtYWlsLCBzZXRFbWFpbF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtuYW1lLCBzZXROYW1lXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3doYXRzYXBwLCBzZXRXaGF0c2FwcF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzdGF0dXMsIHNldFN0YXR1c10gPSB1c2VTdGF0ZTwnaWRsZScgfCAnc3VjY2VzcycgfCAnZXJyb3InPignaWRsZScpO1xuICBjb25zdCBbbWVzc2FnZSwgc2V0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBcbiAgICBpZiAoIWVtYWlsKSB7XG4gICAgICBzZXRTdGF0dXMoJ2Vycm9yJyk7XG4gICAgICBzZXRNZXNzYWdlKCdQbGVhc2UgZW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNTdWJtaXR0aW5nKHRydWUpO1xuICAgIHNldFN0YXR1cygnaWRsZScpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvbmV3c2xldHRlcicsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZW1haWwsXG4gICAgICAgICAgbmFtZTogbmFtZSB8fCB1bmRlZmluZWQsXG4gICAgICAgICAgd2hhdHNhcHA6IHdoYXRzYXBwIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICBzb3VyY2UsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0U3RhdHVzKCdzdWNjZXNzJyk7XG4gICAgICAgIHNldE1lc3NhZ2UoZGF0YS5tZXNzYWdlIHx8ICdTdWNjZXNzZnVsbHkgc3Vic2NyaWJlZCB0byBuZXdzbGV0dGVyIScpO1xuICAgICAgICBzZXRFbWFpbCgnJyk7XG4gICAgICAgIHNldE5hbWUoJycpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0U3RhdHVzKCdlcnJvcicpO1xuICAgICAgICBzZXRNZXNzYWdlKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBzdWJzY3JpYmUuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldFN0YXR1cygnZXJyb3InKTtcbiAgICAgIHNldE1lc3NhZ2UoJ05ldHdvcmsgZXJyb3IuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3VibWl0dGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBcbiAgICAgIGNsYXNzTmFtZT17YHJvdW5kZWQtMnhsIHAtNiBsZzpwLTggJHtjbGFzc05hbWV9YH1cbiAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvciB9fVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWQgbXgtYXV0byB0ZXh0LWNlbnRlclwiPlxuICAgICAgICB7LyogSWNvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctZ3JlZW4tMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFRpdGxlIGFuZCBTdWJ0aXRsZSAqL31cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgbGc6dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgIHt0aXRsZX1cbiAgICAgICAgPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02IHRleHQtc20gbGc6dGV4dC1iYXNlXCI+XG4gICAgICAgICAge3N1YnRpdGxlfVxuICAgICAgICA8L3A+XG5cbiAgICAgICAgey8qIEZvcm0gKi99XG4gICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgIHsvKiBDb21wYWN0IElucHV0IFJvdyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiWW91ciBuYW1lIChvcHRpb25hbClcIlxuICAgICAgICAgICAgICB2YWx1ZT17bmFtZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROYW1lKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMi41IGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZ3JlZW4tNTAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDAgYmctd2hpdGUgdGV4dC1ncmF5LTkwMCBwbGFjZWhvbGRlci1ncmF5LTUwMCB0ZXh0LXNtXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRlbFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV2hhdHNBcHAgKG9wdGlvbmFsKVwiXG4gICAgICAgICAgICAgIHZhbHVlPXt3aGF0c2FwcH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRXaGF0c2FwcChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIuNSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWdyZWVuLTUwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwIGJnLXdoaXRlIHRleHQtZ3JheS05MDAgcGxhY2Vob2xkZXItZ3JheS01MDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEVtYWlsIElucHV0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2VtYWlsfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVtYWlsKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTMgcHktMi41IGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZ3JlZW4tNTAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDAgYmctd2hpdGUgdGV4dC1ncmF5LTkwMCBwbGFjZWhvbGRlci1ncmF5LTUwMCB0ZXh0LXNtXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nIHx8IHN0YXR1cyA9PT0gJ3N1Y2Nlc3MnfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTIuNSByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdGV4dC1zbSB3aGl0ZXNwYWNlLW5vd3JhcCAke1xuICAgICAgICAgICAgICAgIHN0YXR1cyA9PT0gJ3N1Y2Nlc3MnXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi02MDAgY3Vyc29yLWRlZmF1bHQnXG4gICAgICAgICAgICAgICAgICA6IGlzU3VibWl0dGluZ1xuICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNDAwIGN1cnNvci1ub3QtYWxsb3dlZCdcbiAgICAgICAgICAgICAgICAgIDogJ2JnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgYWN0aXZlOmJnLWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc1N1Ym1pdHRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG1yLTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFN1YnNjcmliaW5nLi4uXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiBzdGF0dXMgPT09ICdzdWNjZXNzJyA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgIFN1YnNjcmliZWQhXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICBTdWJzY3JpYmVcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG5cbiAgICAgICAgey8qIFN0YXR1cyBNZXNzYWdlICovfVxuICAgICAgICB7bWVzc2FnZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtdC00IHAtMyByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtc20gJHtcbiAgICAgICAgICAgIHN0YXR1cyA9PT0gJ3N1Y2Nlc3MnXG4gICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCdcbiAgICAgICAgICAgICAgOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgYm9yZGVyIGJvcmRlci1yZWQtMjAwJ1xuICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgIHtzdGF0dXMgPT09ICdzdWNjZXNzJyA/IChcbiAgICAgICAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMiBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTIgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAge21lc3NhZ2V9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFByaXZhY3kgTm90ZSAqL31cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTRcIj5cbiAgICAgICAgICBXZSByZXNwZWN0IHlvdXIgcHJpdmFjeS4gVW5zdWJzY3JpYmUgYXQgYW55IHRpbWUuXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTmV3c2xldHRlclNpZ251cDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiTWFpbCIsIkNoZWNrIiwiQWxlcnRDaXJjbGUiLCJTcGFya2xlcyIsIk5ld3NsZXR0ZXJTaWdudXAiLCJ0aXRsZSIsInN1YnRpdGxlIiwiYmFja2dyb3VuZENvbG9yIiwiY2xhc3NOYW1lIiwic291cmNlIiwiZW1haWwiLCJzZXRFbWFpbCIsIm5hbWUiLCJzZXROYW1lIiwid2hhdHNhcHAiLCJzZXRXaGF0c2FwcCIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsInN0YXR1cyIsInNldFN0YXR1cyIsIm1lc3NhZ2UiLCJzZXRNZXNzYWdlIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInVuZGVmaW5lZCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwiZGl2Iiwic3R5bGUiLCJoMyIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJyZXF1aXJlZCIsImJ1dHRvbiIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NewsletterSignup.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/TestimonialsSection.tsx":
/*!************************************************!*\
  !*** ./app/components/TestimonialsSection.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Quote,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Quote,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst TestimonialsSection = ({ title = \"What Our Customers Say\", subtitle = \"Real reviews from real customers who love our natural skincare\", backgroundColor = \"#f0fdf4\" })=>{\n    const [testimonials, setTestimonials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTestimonials = async ()=>{\n            try {\n                const response = await fetch(\"/api/testimonials?active=true\");\n                const data = await response.json();\n                if (data.success) {\n                    setTestimonials(data.data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching testimonials:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchTestimonials();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 py-8 lg:px-8 lg:py-12\",\n        style: {\n            backgroundColor\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8 lg:mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2 lg:text-3xl\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 lg:text-lg\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1 mb-3\",\n                                    children: [\n                                        ...Array(5)\n                                    ].map((_, j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 bg-gray-200 rounded\"\n                                        }, j, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, undefined) : testimonials.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No testimonials available at the moment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-500 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        ...Array(5)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: `w-4 h-4 ${i < testimonial.rating ? \"text-yellow-400 fill-current\" : \"text-gray-300\"}`\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 text-sm mb-4 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold text-gray-900 text-sm\",\n                                            children: testimonial.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        testimonial.position && testimonial.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-xs\",\n                                            children: [\n                                                testimonial.position,\n                                                \" at \",\n                                                testimonial.company\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, testimonial.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 mr-1 fill-current\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined),\n                            \"4.9/5 Average Rating from 500+ Reviews\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\TestimonialsSection.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestimonialsSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/TestimonialsSection.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/pages/Home.tsx":
/*!***************************************!*\
  !*** ./app/components/pages/Home.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Heart,Leaf,RotateCcw,Star,TrendingUp,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _NewsletterSignup__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../NewsletterSignup */ \"(ssr)/./app/components/NewsletterSignup.tsx\");\n/* harmony import */ var _Hero__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Hero */ \"(ssr)/./app/components/Hero.tsx\");\n/* harmony import */ var _TestimonialsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../TestimonialsSection */ \"(ssr)/./app/components/TestimonialsSection.tsx\");\n/* harmony import */ var _CountdownBanner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../CountdownBanner */ \"(ssr)/./app/components/CountdownBanner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst Home = ()=>{\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        fetchHomepageData();\n    }, []);\n    const fetchHomepageData = async ()=>{\n        try {\n            // Fetch all homepage data from the new endpoint\n            const homepageRes = await fetch(\"/api/homepage-settings\");\n            const homepageData = await homepageRes.json();\n            if (homepageData.success) {\n                // Create a combined array of products\n                const allProducts = [];\n                // Add featured product (Product of the Month) if available\n                if (homepageData.data.featuredProduct) {\n                    allProducts.push(homepageData.data.featuredProduct);\n                }\n                // Add bestsellers\n                if (homepageData.data.bestsellers) {\n                    allProducts.push(...homepageData.data.bestsellers);\n                }\n                setProducts(allProducts);\n                // Set categories\n                if (homepageData.data.categories) {\n                    setCategories(homepageData.data.categories);\n                }\n                // Set homepage settings\n                if (homepageData.data.settings) {\n                    setSettings(homepageData.data.settings);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error fetching homepage data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get Product of the Month (first product)\n    const productOfTheMonth = products[0];\n    // Get Bestsellers (next 4 products)\n    const bestSellers = products.slice(1, 5);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"60937cd6e435cd9f\",\n                children: \"@-webkit-keyframes fadeIn{from{opacity:0}to{opacity:1}}@-moz-keyframes fadeIn{from{opacity:0}to{opacity:1}}@-o-keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@-webkit-keyframes slideIn{from{-webkit-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideIn{from{-moz-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideIn{from{-o-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideIn{from{-webkit-transform:translatey(20px);-moz-transform:translatey(20px);-o-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}.animate-fadeIn.jsx-60937cd6e435cd9f{-webkit-animation:fadeIn.5s ease-in-out;-moz-animation:fadeIn.5s ease-in-out;-o-animation:fadeIn.5s ease-in-out;animation:fadeIn.5s ease-in-out}.animate-slideIn.jsx-60937cd6e435cd9f{-webkit-animation:slideIn.5s ease-out;-moz-animation:slideIn.5s ease-out;-o-animation:slideIn.5s ease-out;animation:slideIn.5s ease-out}\"\n            }, void 0, false, void 0, undefined),\n            settings?.showFlashSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountdownBanner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                title: settings?.flashSaleTitle || \"Weekend Flash Sale\",\n                subtitle: settings?.flashSaleSubtitle || \"Get 25% off all natural skincare products\",\n                endDate: settings?.flashSaleEndDate,\n                backgroundColor: settings?.flashSaleBackgroundColor || \"#16a34a\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, undefined),\n            settings?.showHero && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Hero__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                title: settings?.heroTitle,\n                subtitle: settings?.heroSubtitle,\n                ctaText: settings?.heroCtaText,\n                ctaLink: settings?.heroCtaLink,\n                secondaryCtaText: settings?.heroSecondaryCtaText,\n                secondaryCtaLink: settings?.heroSecondaryCtaLink,\n                badgeText: settings?.heroBadgeText,\n                backgroundColor: settings?.heroBackgroundColor,\n                trustIndicators: {\n                    value1: settings?.trustIndicator1Value,\n                    label1: settings?.trustIndicator1Label,\n                    value2: settings?.trustIndicator2Value,\n                    label2: settings?.trustIndicator2Label,\n                    value3: settings?.trustIndicator3Value,\n                    label3: settings?.trustIndicator3Label,\n                    value4: settings?.trustIndicator4Value,\n                    label4: settings?.trustIndicator4Label\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, undefined),\n            settings?.showBanner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"px-4 mt-4 grid grid-cols-2 gap-3 lg:px-8 lg:mt-6 lg:gap-4\",\n                children: settings?.bannerText ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: settings.bannerBackgroundColor || \"#22c55e\"\n                    },\n                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"rounded-xl p-4 text-white shadow-lg col-span-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-bold text-sm mb-1 lg:text-base\",\n                            children: settings.bannerText\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.bannerCtaText && settings.bannerCtaLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: settings.bannerCtaLink,\n                            className: \"inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90\",\n                            children: [\n                                settings.bannerCtaText,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"ml-1 w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"bg-gradient-to-r from-pink-500 to-rose-500 rounded-xl p-4 text-white shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-bold text-sm mb-1 lg:text-base\",\n                                    children: \"Summer Sale\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs opacity-90 mb-2 lg:text-sm\",\n                                    children: \"Up to 30% off on selected items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/shop?sale=summer\",\n                                    className: \"inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90\",\n                                    children: [\n                                        \"Shop Deals\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"ml-1 w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl p-4 text-white shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-bold text-sm mb-1 lg:text-base\",\n                                    children: \"New Arrivals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs opacity-90 mb-2 lg:text-sm\",\n                                    children: \"Discover our latest products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/shop?filter=new\",\n                                    className: \"inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90\",\n                                    children: [\n                                        \"Explore Now\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"ml-1 w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, undefined),\n            settings?.showProductOfMonth && productOfTheMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"mx-4 mt-6 lg:mx-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: settings?.productSectionBgColor || \"#f0fdf4\"\n                    },\n                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"rounded-2xl p-4 lg:rounded-3xl lg:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center mb-2 lg:mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 mr-1 lg:w-5 lg:h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs font-semibold lg:text-sm text-green-800\",\n                                    children: \"PRODUCT OF THE MONTH\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center space-x-3 lg:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center lg:w-20 lg:h-20\",\n                                    children: productOfTheMonth.images?.[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: productOfTheMonth.images[0].url,\n                                        alt: productOfTheMonth.name,\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"w-full h-full object-cover rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-bold text-gray-900 text-sm lg:text-base\",\n                                            children: productOfTheMonth.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-gray-600 text-xs mt-1 line-clamp-2 lg:text-sm\",\n                                            children: productOfTheMonth.shortDescription || productOfTheMonth.description?.substring(0, 80) + \"...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center\",\n                                                children: [\n                                                    [\n                                                        ...Array(5)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: `w-3 h-3 ${i < Math.floor(productOfTheMonth._count?.reviews / 2) ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\"}`\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, undefined)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs ml-1 text-gray-600\",\n                                                        children: [\n                                                            \"(\",\n                                                            productOfTheMonth._count?.reviews || 0,\n                                                            \" reviews)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center justify-between mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-base font-bold lg:text-lg text-gray-900\",\n                                                    children: [\n                                                        \"₹\",\n                                                        productOfTheMonth.price\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: `/product/${productOfTheMonth.slug}`,\n                                                    className: \"bg-green-600 text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-green-700 transition-colors lg:px-4 lg:py-1.5 lg:text-sm\",\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, undefined),\n            settings?.showCategories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"px-4 mt-8 lg:px-8 lg:mt-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center justify-between mb-5 lg:mb-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-2xl font-bold text-gray-800 lg:text-3xl\",\n                                children: \"Shop by Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/categories\",\n                                className: \"inline-flex items-center text-green-600 text-sm font-medium hover:text-green-700 transition-colors lg:text-base\",\n                                children: [\n                                    \"See All\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"ml-1 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"grid grid-cols-2 gap-4 lg:grid-cols-3 lg:gap-5\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: `/shop?category=${category.slug}`,\n                                className: \"bg-white p-5 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 lg:p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-semibold text-gray-800 text-base lg:text-lg mb-2\",\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-sm text-gray-600 mt-1 lg:text-base\",\n                                        children: [\n                                            category._count?.products || 0,\n                                            \" products\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"mt-3 inline-flex items-center text-xs text-green-600 font-medium\",\n                                        children: [\n                                            \"Shop now\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"ml-1 w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, undefined),\n            settings?.showBestsellers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"px-4 mt-8 mb-8 lg:px-8 lg:mt-10 lg:mb-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center justify-between mb-5 lg:mb-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-2xl font-bold text-gray-800 lg:text-3xl\",\n                                children: \"Best Sellers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center text-amber-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1 lg:w-5 lg:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs font-medium lg:text-sm\",\n                                        children: \"Trending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"space-y-4 lg:space-y-5\",\n                        children: bestSellers.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: `/product/${product.slug}`,\n                                className: \"flex items-center space-x-4 bg-white p-4 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 lg:p-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"w-16 h-16 bg-gray-100 rounded-xl flex-shrink-0 lg:w-20 lg:h-20\",\n                                        children: product.images?.[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: product.images[0].url,\n                                            alt: product.name,\n                                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"w-full h-full object-cover rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-medium text-gray-800 text-base lg:text-lg\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex items-center\",\n                                                    children: [\n                                                        [\n                                                            ...Array(5)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: `w-3 h-3 ${i < Math.floor(product._count?.reviews / 2) ? \"fill-amber-400 text-amber-400\" : \"text-gray-300\"}`\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, undefined)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs ml-1 text-gray-500\",\n                                                            children: [\n                                                                \"(\",\n                                                                product._count?.reviews || 0,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-green-600 font-medium text-base lg:text-lg mt-1\",\n                                                children: [\n                                                    \"₹\",\n                                                    product.price\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex flex-col items-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400 lg:w-6 lg:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-xs text-gray-500 mt-2\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, undefined),\n            settings?.showTestimonials && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"mb-12 lg:mb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TestimonialsSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    title: settings?.testimonialsTitle || \"What Our Customers Say\",\n                    subtitle: settings?.testimonialsSubtitle || \"Real reviews from real customers who love our natural skincare\",\n                    backgroundColor: settings?.testimonialsBackgroundColor || \"#f0fdf4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, undefined),\n            settings?.showNewsletter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"mx-4 mt-8 lg:mx-8 lg:mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NewsletterSignup__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    title: settings?.newsletterTitle || \"Stay Updated\",\n                    subtitle: settings?.newsletterSubtitle || \"Get the latest updates on new products and exclusive offers\",\n                    backgroundColor: settings?.productSectionBgColor || \"#f0fdf4\",\n                    source: \"homepage\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, undefined),\n            settings?.showTrustBadges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"px-4 pb-8 lg:px-8 lg:pb-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-60937cd6e435cd9f\" + \" \" + \"bg-white rounded-2xl p-6 shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"grid grid-cols-2 gap-4 md:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex flex-col items-center text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-600 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-medium text-gray-900 text-sm\",\n                                        children: \"100% Natural\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-gray-500 text-xs mt-1\",\n                                        children: \"Plant-based ingredients\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex flex-col items-center text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-600 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-medium text-gray-900 text-sm\",\n                                        children: \"Free Shipping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-gray-500 text-xs mt-1\",\n                                        children: \"On orders over ₹1000\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex flex-col items-center text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-600 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-medium text-gray-900 text-sm\",\n                                        children: \"Vegan\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-gray-500 text-xs mt-1\",\n                                        children: \"Plant-based formulas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-60937cd6e435cd9f\" + \" \" + \"flex flex-col items-center text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Heart_Leaf_RotateCcw_Star_TrendingUp_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-600 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"font-medium text-gray-900 text-sm\",\n                                        children: \"Halal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-60937cd6e435cd9f\" + \" \" + \"text-gray-500 text-xs mt-1\",\n                                        children: \"Certified halal ingredients\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Home.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/pages/Home.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// localStorage utilities\nconst CART_STORAGE_KEY = \"herbalicious_cart\";\nconst saveCartToStorage = (state)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error saving cart to localStorage:\", error);\n    }\n};\nconst loadCartFromStorage = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error loading cart from localStorage:\", error);\n    }\n    return null;\n};\n// Helper function to generate unique variant key\nconst generateVariantKey = (productId, selectedVariants)=>{\n    if (!selectedVariants || selectedVariants.length === 0) {\n        return productId;\n    }\n    // Sort variants by name to ensure consistent key generation\n    const sortedVariants = [\n        ...selectedVariants\n    ].sort((a, b)=>a.name.localeCompare(b.name));\n    const variantString = sortedVariants.map((v)=>`${v.name}:${v.value}`).join(\"|\");\n    return `${productId}__${variantString}`;\n};\n// Helper function to get item identifier (with fallback for backward compatibility)\nconst getItemIdentifier = (item)=>{\n    return item.variantKey || item.product?.id || item.id;\n};\nconst getInitialCartState = ()=>{\n    const storedCart = loadCartFromStorage();\n    if (storedCart) {\n        return storedCart;\n    }\n    return {\n        items: [],\n        total: 0,\n        subtotal: 0,\n        itemCount: 0,\n        finalTotal: 0,\n        coupons: {\n            appliedCoupons: [],\n            totalDiscount: 0,\n            availableCoupons: []\n        }\n    };\n};\nconst calculateTotals = (items, appliedCoupons)=>{\n    const subtotal = items.reduce((sum, item)=>sum + item.product.price * item.quantity, 0);\n    const itemCount = items.reduce((sum, item)=>sum + item.quantity, 0);\n    const totalDiscount = appliedCoupons.reduce((sum, coupon)=>sum + coupon.discountAmount, 0);\n    const finalTotal = subtotal - totalDiscount;\n    return {\n        subtotal,\n        itemCount,\n        total: subtotal,\n        finalTotal,\n        totalDiscount\n    };\n};\nconst cartReducer = (state, action)=>{\n    let newState;\n    switch(action.type){\n        case \"ADD_ITEM\":\n            {\n                const variantKey = generateVariantKey(action.payload.id, action.selectedVariants);\n                const existingItem = state.items.find((item)=>getItemIdentifier(item) === variantKey);\n                let updatedItems;\n                if (existingItem) {\n                    // Same product with same variants - increase quantity\n                    updatedItems = state.items.map((item)=>getItemIdentifier(item) === variantKey ? {\n                            ...item,\n                            quantity: item.quantity + 1,\n                            variantKey\n                        } : item);\n                } else {\n                    // New product or different variant combination - add as new item\n                    const newCartItem = {\n                        product: action.payload,\n                        quantity: 1,\n                        selectedVariants: action.selectedVariants || [],\n                        variantKey\n                    };\n                    updatedItems = [\n                        ...state.items,\n                        newCartItem\n                    ];\n                }\n                const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: updatedItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"REMOVE_ITEM\":\n            {\n                const filteredItems = state.items.filter((item)=>getItemIdentifier(item) !== action.payload);\n                const totals = calculateTotals(filteredItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: filteredItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"UPDATE_QUANTITY\":\n            {\n                const updatedItems = state.items.map((item)=>getItemIdentifier(item) === action.payload.id ? {\n                        ...item,\n                        quantity: action.payload.quantity\n                    } : item).filter((item)=>item.quantity > 0);\n                const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: updatedItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"APPLY_COUPON\":\n            {\n                // Check if coupon is already applied\n                const isAlreadyApplied = state.coupons.appliedCoupons.some((coupon)=>coupon.coupon.id === action.payload.coupon.id);\n                if (isAlreadyApplied) {\n                    return state;\n                }\n                // Check stacking rules\n                const hasNonStackableCoupon = state.coupons.appliedCoupons.some((coupon)=>!coupon.coupon.isStackable);\n                if (hasNonStackableCoupon && !action.payload.coupon.isStackable) {\n                    return state;\n                }\n                const updatedAppliedCoupons = [\n                    ...state.coupons.appliedCoupons,\n                    action.payload\n                ];\n                const totals = calculateTotals(state.items, updatedAppliedCoupons);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"REMOVE_COUPON\":\n            {\n                const updatedAppliedCoupons = state.coupons.appliedCoupons.filter((coupon)=>coupon.coupon.id !== action.payload);\n                const totals = calculateTotals(state.items, updatedAppliedCoupons);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"CLEAR_COUPONS\":\n            {\n                const totals = calculateTotals(state.items, []);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    }\n                };\n                break;\n            }\n        case \"CLEAR_CART\":\n            {\n                newState = {\n                    items: [],\n                    total: 0,\n                    subtotal: 0,\n                    itemCount: 0,\n                    finalTotal: 0,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    }\n                };\n                break;\n            }\n        default:\n            return state;\n    }\n    // Save to localStorage after state change\n    saveCartToStorage(newState);\n    return newState;\n};\nconst CartProvider = ({ children })=>{\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(cartReducer, getInitialCartState());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            state,\n            dispatch\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\CartContext.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (!context) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlashSaleProvider: () => (/* binding */ FlashSaleProvider),\n/* harmony export */   useFlashSale: () => (/* binding */ useFlashSale)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FlashSaleProvider,useFlashSale auto */ \n\nconst FlashSaleContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction FlashSaleProvider({ children }) {\n    const [flashSaleSettings, setFlashSaleSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchFlashSaleSettings = async ()=>{\n        try {\n            const response = await fetch(\"/api/homepage-settings\");\n            const data = await response.json();\n            if (data.success && data.data.settings) {\n                const settings = data.data.settings;\n                setFlashSaleSettings({\n                    showFlashSale: settings.showFlashSale,\n                    flashSaleEndDate: settings.flashSaleEndDate,\n                    flashSalePercentage: settings.flashSalePercentage,\n                    flashSaleTitle: settings.flashSaleTitle,\n                    flashSaleSubtitle: settings.flashSaleSubtitle,\n                    flashSaleBackgroundColor: settings.flashSaleBackgroundColor\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching flash sale settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFlashSaleSettings();\n    }, []);\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await fetchFlashSaleSettings();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashSaleContext.Provider, {\n        value: {\n            flashSaleSettings,\n            loading,\n            refreshSettings\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\FlashSaleContext.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\nfunction useFlashSale() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FlashSaleContext);\n    if (context === undefined) {\n        throw new Error(\"useFlashSale must be used within a FlashSaleProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/FlashSaleContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useNotifications,NotificationProvider,default auto */ \n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n};\nconst NotificationProvider = ({ children })=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (options = {})=>{\n        if (!session?.user?.id) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const params = new URLSearchParams({\n                page: (options.page || 1).toString(),\n                limit: (options.limit || 10).toString(),\n                ...options.unreadOnly && {\n                    unreadOnly: \"true\"\n                }\n            });\n            const response = await fetch(`/api/notifications?${params}`);\n            const data = await response.json();\n            if (data.success) {\n                setNotifications(data.data.notifications);\n                setUnreadCount(data.data.unreadCount);\n            } else {\n                setError(data.error || \"Failed to fetch notifications\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching notifications:\", error);\n            setError(\"Failed to fetch notifications\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const refreshUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/unread-count\");\n            const data = await response.json();\n            if (data.success) {\n                setUnreadCount(data.unreadCount);\n            }\n        } catch (error) {\n            console.error(\"Error fetching unread count:\", error);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (notificationId)=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(`/api/notifications/${notificationId}/read`, {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                            ...notification,\n                            isRead: true\n                        } : notification));\n                // Update unread count\n                setUnreadCount((prev)=>Math.max(0, prev - 1));\n            } else {\n                setError(data.error || \"Failed to mark notification as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n            setError(\"Failed to mark notification as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAllAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/mark-all-read\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>({\n                            ...notification,\n                            isRead: true\n                        })));\n                setUnreadCount(0);\n            } else {\n                setError(data.error || \"Failed to mark all notifications as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n            setError(\"Failed to mark all notifications as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    // Fetch notifications when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"authenticated\" && session?.user?.id) {\n            fetchNotifications({\n                limit: 5\n            }); // Fetch recent notifications for dropdown\n            refreshUnreadCount();\n        }\n    }, [\n        status,\n        session?.user?.id,\n        fetchNotifications,\n        refreshUnreadCount\n    ]);\n    // Refresh unread count periodically (every 30 seconds)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!session?.user?.id) return;\n        const interval = setInterval(()=>{\n            refreshUnreadCount();\n        }, 30000); // 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        session?.user?.id,\n        refreshUnreadCount\n    ]);\n    const value = {\n        notifications,\n        unreadCount,\n        loading,\n        error,\n        fetchNotifications,\n        markAsRead,\n        markAllAsRead,\n        refreshUnreadCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\NotificationContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthSessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRDtBQU9uQyxTQUFTQyxvQkFBb0IsRUFBRUMsUUFBUSxFQUFTO0lBQzdELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2NvbnRleHQvU2Vzc2lvblByb3ZpZGVyLnRzeD82MTQxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XHJcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFByb3BzKSB7XHJcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPjtcclxufSJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/context/SessionProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ae750f5340b2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzPzIzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZTc1MGY1MzQwYjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/Layout.tsx":
/*!***********************************!*\
  !*** ./app/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/components/pages/Home.tsx":
/*!***************************************!*\
  !*** ./app/components/pages/Home.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Home.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e0),
/* harmony export */   useCart: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);


/***/ }),

/***/ "(rsc)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlashSaleProvider: () => (/* binding */ e0),
/* harmony export */   useFlashSale: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#FlashSaleProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#useFlashSale`);


/***/ }),

/***/ "(rsc)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ e1),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useNotifications: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/CartContext */ \"(rsc)/./app/context/CartContext.tsx\");\n/* harmony import */ var _context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/SessionProvider */ \"(rsc)/./app/context/SessionProvider.tsx\");\n/* harmony import */ var _context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./context/NotificationContext */ \"(rsc)/./app/context/NotificationContext.tsx\");\n/* harmony import */ var _context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context/FlashSaleContext */ \"(rsc)/./app/context/FlashSaleContext.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Herbalicious - Natural Skincare\",\n    description: \"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients.\"\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1.0,\n    themeColor: \"#16a34a\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.NotificationProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__.FlashSaleProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_CartContext__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/Layout */ \"(rsc)/./app/components/Layout.tsx\");\n/* harmony import */ var _components_pages_Home__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/pages/Home */ \"(rsc)/./app/components/pages/Home.tsx\");\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_Home__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\page.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdDO0FBQ0U7QUFFM0IsU0FBU0U7SUFDdEIscUJBQ0UsOERBQUNGLDBEQUFNQTtrQkFDTCw0RUFBQ0MsOERBQUlBOzs7Ozs7Ozs7O0FBR1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExheW91dCBmcm9tICcuL2NvbXBvbmVudHMvTGF5b3V0J1xuaW1wb3J0IEhvbWUgZnJvbSAnLi9jb21wb25lbnRzL3BhZ2VzL0hvbWUnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWVQYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxMYXlvdXQ+XG4gICAgICA8SG9tZSAvPlxuICAgIDwvTGF5b3V0PlxuICApXG59Il0sIm5hbWVzIjpbIkxheW91dCIsIkhvbWUiLCJIb21lUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();