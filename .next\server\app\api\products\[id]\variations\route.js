"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/[id]/variations/route";
exports.ids = ["app/api/products/[id]/variations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_products_id_variations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/[id]/variations/route.ts */ \"(rsc)/./app/api/products/[id]/variations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/[id]/variations/route\",\n        pathname: \"/api/products/[id]/variations\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/[id]/variations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\products\\\\[id]\\\\variations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_products_id_variations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/[id]/variations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/[id]/variations/route.ts":
/*!***************************************************!*\
  !*** ./app/api/products/[id]/variations/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../lib/errors */ \"(rsc)/./app/lib/errors.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../lib/logger */ \"(rsc)/./app/lib/logger.ts\");\n\n\n\n\n\n\nconst GET = (0,_lib_errors__WEBPACK_IMPORTED_MODULE_4__.asyncHandler)(async (request, { params })=>{\n    const productId = params.id;\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.apiRequest(\"GET\", `/api/products/${productId}/variations`);\n    // Fetch all variations for the product\n    const variations = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.productVariant.findMany({\n        where: {\n            productId: productId\n        },\n        orderBy: [\n            {\n                name: \"asc\"\n            },\n            {\n                value: \"asc\"\n            }\n        ]\n    });\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.info(\"Product variations fetched\", {\n        productId,\n        count: variations.length\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        data: variations\n    });\n});\nconst POST = (0,_lib_errors__WEBPACK_IMPORTED_MODULE_4__.asyncHandler)(async (request, { params })=>{\n    const productId = params.id;\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.apiRequest(\"POST\", `/api/products/${productId}/variations`);\n    // Check authentication\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.AuthenticationError();\n    }\n    if (session.user.role !== \"ADMIN\") {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.AuthorizationError();\n    }\n    const body = await request.json();\n    const { name, value, price, pricingMode } = body;\n    // Validate required fields\n    if (!name || !value) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.ValidationError(\"Name and value are required\");\n    }\n    // Validate pricing mode\n    const validPricingModes = [\n        \"REPLACE\",\n        \"INCREMENT\",\n        \"FIXED\"\n    ];\n    const validatedPricingMode = pricingMode && validPricingModes.includes(pricingMode) ? pricingMode : \"REPLACE\";\n    // Check if product exists\n    const product = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findUnique({\n        where: {\n            id: productId\n        }\n    });\n    if (!product) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.NotFoundError(\"Product\");\n    }\n    // Check if variation with same name and value already exists\n    const existingVariation = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.productVariant.findFirst({\n        where: {\n            productId: productId,\n            name: name,\n            value: value\n        }\n    });\n    if (existingVariation) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.ConflictError(\"Variation with this name and value already exists\");\n    }\n    // Parse and validate price\n    let parsedPrice = null;\n    if (price !== undefined && price !== null && price !== \"\") {\n        const numPrice = typeof price === \"string\" ? parseFloat(price) : Number(price);\n        if (!isNaN(numPrice) && isFinite(numPrice)) {\n            parsedPrice = numPrice;\n        }\n    }\n    // Create the variation\n    const variation = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.productVariant.create({\n        data: {\n            name: name.trim(),\n            value: value.trim(),\n            price: parsedPrice,\n            pricingMode: validatedPricingMode,\n            productId: productId\n        }\n    });\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.info(\"Product variation created\", {\n        productId,\n        variationId: variation.id,\n        name,\n        value,\n        userId: session.user.id\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        data: variation,\n        message: \"Variation created successfully\"\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/[id]/variations/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_db__WEBPACK_IMPORTED_MODULE_4__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // If this is the first time the user signs in\n            if (user) {\n                token.sub = user.id;\n                token.role = user.role;\n            }\n            // For OAuth providers, ensure we get the correct user ID from database\n            if (account && token.email) {\n                try {\n                    const dbUser = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: token.email\n                        },\n                        select: {\n                            id: true,\n                            role: true\n                        }\n                    });\n                    if (dbUser) {\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                    }\n                } catch (error) {\n                    // Log error in development only\n                    if (true) {\n                        console.error(\"Error fetching user in JWT callback:\", error);\n                    }\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Always try to get fresh user data from database\n            if (token.email) {\n                try {\n                    const dbUser = await _db__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: token.email\n                        },\n                        select: {\n                            id: true,\n                            role: true,\n                            email: true,\n                            name: true\n                        }\n                    });\n                    if (dbUser) {\n                        return {\n                            ...session,\n                            user: {\n                                ...session.user,\n                                id: dbUser.id,\n                                role: dbUser.role,\n                                email: dbUser.email,\n                                name: dbUser.name\n                            }\n                        };\n                    }\n                } catch (error) {\n                    // Log error in development only\n                    if (true) {\n                        console.error(\"Error fetching user in session callback:\", error);\n                    }\n                }\n            }\n            // Fallback: if we have token.sub but no database lookup worked\n            if (session.user && token.sub) {\n                return {\n                    ...session,\n                    user: {\n                        ...session.user,\n                        id: token.sub,\n                        role: token.role\n                    }\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/errors.ts":
/*!***************************!*\
  !*** ./app/lib/errors.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppError: () => (/* binding */ AppError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   AuthorizationError: () => (/* binding */ AuthorizationError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   DatabaseError: () => (/* binding */ DatabaseError),\n/* harmony export */   ErrorResponses: () => (/* binding */ ErrorResponses),\n/* harmony export */   ExternalServiceError: () => (/* binding */ ExternalServiceError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   asyncHandler: () => (/* binding */ asyncHandler),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   handlePrismaError: () => (/* binding */ handlePrismaError),\n/* harmony export */   handleZodError: () => (/* binding */ handleZodError),\n/* harmony export */   isAppError: () => (/* binding */ isAppError)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/errors.js\");\n\n\nclass AppError extends Error {\n    constructor(message, statusCode = 500, code = \"INTERNAL_ERROR\", details){\n        super(message);\n        this.statusCode = statusCode;\n        this.code = code;\n        this.details = details;\n        this.name = \"AppError\";\n        // Maintains proper stack trace for where our error was thrown (only available on V8)\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, AppError);\n        }\n    }\n}\nclass ValidationError extends AppError {\n    constructor(message, details){\n        super(message, 400, \"VALIDATION_ERROR\", details);\n        this.name = \"ValidationError\";\n    }\n}\nclass AuthenticationError extends AppError {\n    constructor(message = \"Authentication required\"){\n        super(message, 401, \"AUTHENTICATION_ERROR\");\n        this.name = \"AuthenticationError\";\n    }\n}\nclass AuthorizationError extends AppError {\n    constructor(message = \"Insufficient permissions\"){\n        super(message, 403, \"AUTHORIZATION_ERROR\");\n        this.name = \"AuthorizationError\";\n    }\n}\nclass NotFoundError extends AppError {\n    constructor(resource = \"Resource\"){\n        super(`${resource} not found`, 404, \"NOT_FOUND_ERROR\");\n        this.name = \"NotFoundError\";\n    }\n}\nclass ConflictError extends AppError {\n    constructor(message){\n        super(message, 409, \"CONFLICT_ERROR\");\n        this.name = \"ConflictError\";\n    }\n}\nclass RateLimitError extends AppError {\n    constructor(message = \"Rate limit exceeded\"){\n        super(message, 429, \"RATE_LIMIT_ERROR\");\n        this.name = \"RateLimitError\";\n    }\n}\nclass DatabaseError extends AppError {\n    constructor(message, details){\n        super(message, 500, \"DATABASE_ERROR\", details);\n        this.name = \"DatabaseError\";\n    }\n}\nclass ExternalServiceError extends AppError {\n    constructor(service, message, details){\n        super(`${service} service error: ${message}`, 502, \"EXTERNAL_SERVICE_ERROR\", details);\n        this.name = \"ExternalServiceError\";\n    }\n}\nfunction handlePrismaError(error) {\n    const prismaError = error;\n    switch(prismaError.code){\n        case \"P2002\":\n            // Unique constraint violation\n            const target = prismaError.meta?.target?.[0] || \"field\";\n            return new ConflictError(`${target} already exists`);\n        case \"P2003\":\n            // Foreign key constraint violation\n            const constraint = prismaError.meta?.constraint;\n            if (constraint?.includes(\"userId\")) {\n                return new AuthenticationError(\"Invalid user session\");\n            }\n            return new ValidationError(\"Invalid reference to related record\");\n        case \"P2025\":\n            // Record not found\n            return new NotFoundError();\n        case \"P2014\":\n            // Required relation violation\n            return new ValidationError(\"Missing required relationship\");\n        case \"P2000\":\n            // Value too long\n            return new ValidationError(\"Input value is too long\");\n        case \"P2001\":\n            // Record does not exist\n            return new NotFoundError();\n        case \"P2004\":\n            // Constraint failed\n            return new ValidationError(\"Data constraint violation\");\n        default:\n            return new DatabaseError(\"Database operation failed\", {\n                code: prismaError.code,\n                message: prismaError.message\n            });\n    }\n}\n// Zod error handling\nfunction handleZodError(error) {\n    const errors = error.issues.map((err)=>({\n            field: err.path.join(\".\"),\n            message: err.message,\n            code: err.code\n        }));\n    return new ValidationError(\"Validation failed\", {\n        errors\n    });\n}\n// Generic error handler for API routes\nfunction handleApiError(error) {\n    // Log error in development\n    if (true) {\n        console.error(\"API Error:\", error);\n    }\n    // Handle known error types\n    if (error instanceof AppError) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: error.code,\n                message: error.message,\n                ...error.details && {\n                    details: error.details\n                }\n            }\n        }, {\n            status: error.statusCode\n        });\n    }\n    // Handle Zod validation errors\n    if (error instanceof zod__WEBPACK_IMPORTED_MODULE_1__.ZodError) {\n        const validationError = handleZodError(error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: validationError.code,\n                message: validationError.message,\n                details: validationError.details\n            }\n        }, {\n            status: validationError.statusCode\n        });\n    }\n    // Handle Prisma errors\n    if (error && typeof error === \"object\" && \"code\" in error && typeof error.code === \"string\") {\n        const prismaError = handlePrismaError(error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: prismaError.code,\n                message: prismaError.message,\n                ...prismaError.details && {\n                    details: prismaError.details\n                }\n            }\n        }, {\n            status: prismaError.statusCode\n        });\n    }\n    // Handle generic errors\n    const message = error instanceof Error ? error.message : \"An unexpected error occurred\";\n    // Don't expose internal error details in production\n    const errorResponse = {\n        success: false,\n        error: {\n            code: \"INTERNAL_ERROR\",\n            message:  false ? 0 : message\n        }\n    };\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(errorResponse, {\n        status: 500\n    });\n}\n// Error response helpers\nconst ErrorResponses = {\n    unauthorized: ()=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"UNAUTHORIZED\",\n                message: \"Authentication required\"\n            }\n        }, {\n            status: 401\n        }),\n    forbidden: ()=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"FORBIDDEN\",\n                message: \"Insufficient permissions\"\n            }\n        }, {\n            status: 403\n        }),\n    notFound: (resource = \"Resource\")=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"NOT_FOUND\",\n                message: `${resource} not found`\n            }\n        }, {\n            status: 404\n        }),\n    validation: (message, details)=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"VALIDATION_ERROR\",\n                message,\n                ...details && {\n                    details\n                }\n            }\n        }, {\n            status: 400\n        }),\n    conflict: (message)=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"CONFLICT\",\n                message\n            }\n        }, {\n            status: 409\n        }),\n    rateLimit: ()=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"RATE_LIMIT\",\n                message: \"Rate limit exceeded\"\n            }\n        }, {\n            status: 429\n        }),\n    internal: (message)=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"INTERNAL_ERROR\",\n                message:  false ? 0 : message || \"An unexpected error occurred\"\n            }\n        }, {\n            status: 500\n        })\n};\n// Async error wrapper for API routes\nfunction asyncHandler(fn) {\n    return async (...args)=>{\n        try {\n            return await fn(...args);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n// Type guard for checking if error is an AppError\nfunction isAppError(error) {\n    return error instanceof AppError;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/errors.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/logger.ts":
/*!***************************!*\
  !*** ./app/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   createRequestLogger: () => (/* binding */ createRequestLogger),\n/* harmony export */   devLog: () => (/* binding */ devLog),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar LogLevel;\n(function(LogLevel) {\n    LogLevel[LogLevel[\"ERROR\"] = 0] = \"ERROR\";\n    LogLevel[LogLevel[\"WARN\"] = 1] = \"WARN\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"DEBUG\"] = 3] = \"DEBUG\";\n})(LogLevel || (LogLevel = {}));\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === \"development\";\n        // Reduce verbosity in development - only show warnings and errors\n        this.logLevel = this.isDevelopment ? 1 : 2;\n    }\n    shouldLog(level) {\n        return level <= this.logLevel;\n    }\n    formatMessage(entry) {\n        const { timestamp, level, message, context, error, userId, requestId } = entry;\n        const levelName = LogLevel[level];\n        let formatted = `[${timestamp}] ${levelName}: ${message}`;\n        if (userId) {\n            formatted += ` | User: ${userId}`;\n        }\n        if (requestId) {\n            formatted += ` | Request: ${requestId}`;\n        }\n        if (context && Object.keys(context).length > 0) {\n            formatted += ` | Context: ${JSON.stringify(context)}`;\n        }\n        if (error) {\n            formatted += ` | Error: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formatted += `\\nStack: ${error.stack}`;\n            }\n        }\n        return formatted;\n    }\n    log(level, message, context, error) {\n        if (!this.shouldLog(level)) return;\n        const entry = {\n            timestamp: new Date().toISOString(),\n            level,\n            message,\n            context,\n            error\n        };\n        const formatted = this.formatMessage(entry);\n        // In development, use console methods for better formatting\n        if (this.isDevelopment) {\n            switch(level){\n                case 0:\n                    console.error(formatted);\n                    break;\n                case 1:\n                    console.warn(formatted);\n                    break;\n                case 2:\n                    console.info(formatted);\n                    break;\n                case 3:\n                    console.debug(formatted);\n                    break;\n            }\n        } else {\n            // In production, use structured logging (JSON format)\n            console.log(JSON.stringify(entry));\n        }\n    }\n    error(message, error, context) {\n        this.log(0, message, context, error);\n    }\n    warn(message, context) {\n        this.log(1, message, context);\n    }\n    info(message, context) {\n        this.log(2, message, context);\n    }\n    debug(message, context) {\n        this.log(3, message, context);\n    }\n    // API-specific logging methods\n    apiRequest(method, path, userId, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            userId,\n            type: \"api_request\"\n        });\n    }\n    apiResponse(method, path, statusCode, duration, context) {\n        this.info(`API ${method} ${path} - ${statusCode}`, {\n            ...context,\n            statusCode,\n            duration,\n            type: \"api_response\"\n        });\n    }\n    apiError(method, path, error, userId, context) {\n        this.error(`API ${method} ${path} failed`, error, {\n            ...context,\n            userId,\n            type: \"api_error\"\n        });\n    }\n    // Authentication logging\n    authSuccess(userId, method, context) {\n        this.info(`Authentication successful`, {\n            ...context,\n            userId,\n            method,\n            type: \"auth_success\"\n        });\n    }\n    authFailure(email, method, reason, context) {\n        this.warn(`Authentication failed`, {\n            ...context,\n            email,\n            method,\n            reason,\n            type: \"auth_failure\"\n        });\n    }\n    // Database logging\n    dbQuery(operation, table, duration, context) {\n        this.debug(`DB ${operation} on ${table}`, {\n            ...context,\n            operation,\n            table,\n            duration,\n            type: \"db_query\"\n        });\n    }\n    dbError(operation, table, error, context) {\n        this.error(`DB ${operation} on ${table} failed`, error, {\n            ...context,\n            operation,\n            table,\n            type: \"db_error\"\n        });\n    }\n    // Security logging\n    securityEvent(event, severity, context) {\n        const level = severity === \"high\" ? 0 : severity === \"medium\" ? 1 : 2;\n        this.log(level, `Security event: ${event}`, {\n            ...context,\n            severity,\n            type: \"security_event\"\n        });\n    }\n    // Rate limiting logging\n    rateLimitHit(identifier, limit, window, context) {\n        this.warn(`Rate limit exceeded`, {\n            ...context,\n            identifier,\n            limit,\n            window,\n            type: \"rate_limit\"\n        });\n    }\n    // Email logging\n    emailSent(to, subject, template, context) {\n        this.info(`Email sent`, {\n            ...context,\n            to,\n            subject,\n            template,\n            type: \"email_sent\"\n        });\n    }\n    emailError(to, subject, error, context) {\n        this.error(`Email failed to send`, error, {\n            ...context,\n            to,\n            subject,\n            type: \"email_error\"\n        });\n    }\n    // Performance logging\n    performance(operation, duration, context) {\n        const level = duration > 5000 ? 1 : 3;\n        this.log(level, `Performance: ${operation} took ${duration}ms`, {\n            ...context,\n            operation,\n            duration,\n            type: \"performance\"\n        });\n    }\n}\n// Create singleton instance\nconst logger = new Logger();\n// Request logging middleware helper\nfunction createRequestLogger(req) {\n    const requestId = crypto.randomUUID();\n    const startTime = Date.now();\n    return {\n        requestId,\n        log: (message, context)=>{\n            logger.info(message, {\n                ...context,\n                requestId\n            });\n        },\n        error: (message, error, context)=>{\n            logger.error(message, error, {\n                ...context,\n                requestId\n            });\n        },\n        end: (statusCode)=>{\n            const duration = Date.now() - startTime;\n            logger.apiResponse(req.method || \"UNKNOWN\", new URL(req.url || \"\").pathname, statusCode, duration, {\n                requestId\n            });\n        }\n    };\n}\n// Development-only logging helpers\nconst devLog = {\n    info: (message, data)=>{\n        if (true) {\n            console.log(`🔍 ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    error: (message, error)=>{\n        if (true) {\n            console.error(`❌ ${message}`, error);\n        }\n    },\n    warn: (message, data)=>{\n        if (true) {\n            console.warn(`⚠️ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    success: (message, data)=>{\n        if (true) {\n            console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/logger.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();