import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { createPaymentOrder, generateReceiptId, convertToPaise, validatePaymentAmount } from '../../../lib/payment';
import { handleApiError, ValidationError, AuthenticationError, AppError, asyncHandler } from '../../../lib/errors';
import { logger } from '../../../lib/logger';
import { withRateLimit, generalLimiter } from '../../../lib/rate-limit';
import { orderNotifications } from '../../../lib/notification-helpers';

const createOrderSchema = z.object({
  cartItems: z.array(z.object({
    productId: z.string(),
    quantity: z.number().min(1),
    price: z.number().min(0)
  })),
  shippingAddress: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    address1: z.string().min(1),
    address2: z.string().optional(),
    city: z.string().min(1),
    state: z.string().min(1),
    postalCode: z.string().min(1),
    country: z.string().min(1),
    phone: z.string().min(1)
  }),
  totalAmount: z.number().min(1),
  appliedCoupons: z.array(z.object({
    coupon: z.object({
      id: z.string(),
      code: z.string(),
      name: z.string()
    }),
    discountAmount: z.number().min(0)
  })).optional().default([])
});

export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest('POST', '/api/payments/create-order');

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 10);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const body = await request.json();
  const validatedData = createOrderSchema.parse(body);
  const { cartItems, shippingAddress, totalAmount, appliedCoupons } = validatedData;

  logger.info('Creating payment order', { 
    userId: session.user.id,
    totalAmount,
    itemCount: cartItems.length 
  });

  // Validate cart items and calculate total
  let calculatedTotal = 0;
  const orderItems = [];

  for (const item of cartItems) {
    // Verify product exists and price is correct
    const product = await prisma.product.findUnique({
      where: { id: item.productId }
    });

    if (!product) {
      throw new ValidationError(`Product ${item.productId} not found`);
    }

    const itemPrice = product.price || 0;

    // Verify price matches
    if (Math.abs(itemPrice - item.price) > 0.01) {
      throw new ValidationError(`Price mismatch for product ${item.productId}`);
    }

    const itemTotal = itemPrice * item.quantity;
    calculatedTotal += itemTotal;
    
    orderItems.push({
      productId: item.productId,
      quantity: item.quantity,
      price: itemPrice,
      total: itemTotal
    });
  }

  // Calculate coupon discount
  const totalDiscount = appliedCoupons.reduce((sum, coupon) => sum + coupon.discountAmount, 0);
  const subtotal = calculatedTotal;
  const expectedTotal = subtotal - totalDiscount;

  // Verify total amount (should be subtotal minus discounts)
  if (Math.abs(expectedTotal - totalAmount) > 0.01) {
    throw new ValidationError(`Total amount mismatch. Expected: ${expectedTotal}, Received: ${totalAmount}`);
  }

  // Convert to paise for Razorpay
  const amountInPaise = convertToPaise(totalAmount);
  
  // Validate payment amount
  if (!validatePaymentAmount(amountInPaise)) {
    throw new ValidationError('Invalid payment amount');
  }

  // Generate receipt ID and order number
  const receiptId = generateReceiptId('HERB');
  const orderNumber = Math.random().toString(36).substring(2, 8).toUpperCase();

  try {
    // Create Razorpay order first
    const razorpayOrder = await createPaymentOrder({
      amount: amountInPaise,
      currency: 'INR',
      receipt: receiptId,
      notes: {
        userId: session.user.id,
        userEmail: session.user.email || '',
        orderNumber: orderNumber
      }
    });

    // Check if this address already exists for the user
    const existingAddress = await prisma.address.findFirst({
      where: {
        userId: session.user.id,
        firstName: shippingAddress.firstName,
        lastName: shippingAddress.lastName,
        address1: shippingAddress.address1,
        city: shippingAddress.city,
        state: shippingAddress.state,
        postalCode: shippingAddress.postalCode,
        country: shippingAddress.country
      }
    });

    // Create or use existing address in user's address book
    let userAddress;
    if (!existingAddress) {
      userAddress = await prisma.address.create({
        data: {
          userId: session.user.id,
          firstName: shippingAddress.firstName,
          lastName: shippingAddress.lastName,
          address1: shippingAddress.address1,
          address2: shippingAddress.address2,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postalCode: shippingAddress.postalCode,
          country: shippingAddress.country,
          phone: shippingAddress.phone
        }
      });
    }

    // Create order in database
    const order = await prisma.order.create({
      data: {
        orderNumber: orderNumber,
        userId: session.user.id,
        status: 'PENDING',
        paymentStatus: 'PENDING',
        paymentId: razorpayOrder.id,
        subtotal: subtotal,
        couponDiscount: totalDiscount,
        total: totalAmount,
        currency: 'INR',
        notes: `Razorpay Order ID: ${razorpayOrder.id}${appliedCoupons.length > 0 ? ` | Coupons: ${appliedCoupons.map(c => c.coupon.code).join(', ')}` : ''}`,
        address: {
          create: {
            firstName: shippingAddress.firstName,
            lastName: shippingAddress.lastName,
            address1: shippingAddress.address1,
            address2: shippingAddress.address2,
            city: shippingAddress.city,
            state: shippingAddress.state,
            postalCode: shippingAddress.postalCode,
            country: shippingAddress.country,
            phone: shippingAddress.phone
          }
        },
        items: {
          create: orderItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            total: item.total
          }))
        },
        // Create coupon usage records
        ...(appliedCoupons.length > 0 && {
          couponUsages: {
            create: appliedCoupons.map(appliedCoupon => ({
              couponId: appliedCoupon.coupon.id,
              userId: session.user.id,
              usedAt: new Date()
            }))
          }
        })
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        address: true
      }
    });

    logger.info('Payment order created successfully', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      razorpayOrderId: razorpayOrder.id,
      amount: totalAmount,
      userId: session.user.id
    });

    // Send order placed notification
    try {
      await orderNotifications.orderPlaced(session.user.id, {
        orderId: order.id,
        orderNumber: order.orderNumber,
        total: totalAmount,
        currency: 'INR',
        itemCount: cartItems.length,
      });

      logger.info('Order placed notification sent', {
        orderId: order.id,
        userId: session.user.id
      });
    } catch (notificationError) {
      logger.error('Failed to send order placed notification', notificationError as Error);
      // Don't fail the order creation if notification fails
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        razorpayOrderId: razorpayOrder.id,
        amount: amountInPaise,
        currency: 'INR',
        receipt: receiptId
      },
      razorpayKeyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID
    });

  } catch (error) {
    logger.error('Failed to create payment order', error as Error);
    throw new AppError('Failed to create payment order', 500);
  }
});