'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Package, Truck, CheckCircle, Clock, RotateCcw, Eye, XCircle, RefreshCw, Loader2 } from 'lucide-react';
import { useSession } from 'next-auth/react';

interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  total: number;
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
  };
}

interface Order {
  id: string;
  orderNumber: string;
  createdAt: string;
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';
  paymentMethod: 'ONLINE' | 'COD';
  total: number;
  subtotal: number;
  couponDiscount: number;
  items: OrderItem[];
}

const OrderHistory: React.FC = () => {
  const router = useRouter();
  const { data: session } = useSession();
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const statusConfig = {
    PENDING: { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Pending' },
    CONFIRMED: { icon: CheckCircle, color: 'text-blue-600', bg: 'bg-blue-100', label: 'Confirmed' },
    PROCESSING: { icon: Clock, color: 'text-green-600', bg: 'bg-green-100', label: 'Processing' },
    SHIPPED: { icon: Truck, color: 'text-green-600', bg: 'bg-green-100', label: 'Shipped' },
    DELIVERED: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100', label: 'Delivered' },
    CANCELLED: { icon: XCircle, color: 'text-red-600', bg: 'bg-red-100', label: 'Cancelled' },
    REFUNDED: { icon: RefreshCw, color: 'text-purple-600', bg: 'bg-purple-100', label: 'Refunded' }
  };

  useEffect(() => {
    fetchOrders();
  }, [page, selectedFilter]);

  const fetchOrders = async () => {
    if (!session?.user) {
      setError('Please login to view your orders');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });

      if (selectedFilter !== 'all') {
        params.append('status', selectedFilter);
      }

      const response = await fetch(`/api/orders?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch orders');
      }

      setOrders(data.orders);
      setTotalPages(data.pagination.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const filters = [
    { id: 'all', label: 'All Orders', count: orders.length },
    { id: 'PROCESSING', label: 'Processing', count: orders.filter(o => o.status === 'PROCESSING').length },
    { id: 'SHIPPED', label: 'Shipped', count: orders.filter(o => o.status === 'SHIPPED').length },
    { id: 'DELIVERED', label: 'Delivered', count: orders.filter(o => o.status === 'DELIVERED').length }
  ];

  const filteredOrders = orders;
  const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);

  const handleReorder = async (order: Order) => {
    // Add items to cart and redirect to checkout
    const cartItems = order.items.map(item => ({
      id: item.product.id,
      name: item.product.name,
      price: item.product.price,
      quantity: item.quantity,
      slug: item.product.slug
    }));

    // Store in localStorage and redirect
    localStorage.setItem('cart', JSON.stringify(cartItems));
    router.push('/checkout');
  };

  const handleViewDetails = (orderId: string) => {
    router.push(`/orders/${orderId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-green-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen px-4">
        <Package className="w-16 h-16 text-gray-300 mb-4" />
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Unable to load orders</h3>
        <p className="text-gray-600 mb-6">{error}</p>
        <button
          onClick={fetchOrders}
          className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Header */}
        <div className="sticky top-16 bg-white z-30 px-4 py-4 border-b">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-bold text-gray-800">Order History</h1>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 py-6 bg-gradient-to-br from-green-50 to-green-100">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{orders.length}</div>
              <div className="text-sm text-gray-600">Total Orders</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">₹{totalSpent.toFixed(2)}</div>
              <div className="text-sm text-gray-600">Total Spent</div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="px-4 py-4 bg-white border-b">
          <div className="flex space-x-2 overflow-x-auto">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => {
                  setSelectedFilter(filter.id);
                  setPage(1);
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                  selectedFilter === filter.id
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span>{filter.label}</span>
                <span className={`px-2 py-0.5 rounded-full text-xs ${
                  selectedFilter === filter.id
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {filter.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Orders List */}
        <div className="px-4 py-6">
          {filteredOrders.length > 0 ? (
            <div className="space-y-4">
              {filteredOrders.map((order) => {
                const StatusIcon = statusConfig[order.status].icon;
                const statusStyle = statusConfig[order.status];
                
                return (
                  <div key={order.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-800">#{order.orderNumber}</h3>
                        <p className="text-sm text-gray-600">{new Date(order.createdAt).toLocaleDateString()}</p>
                      </div>
                      <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusStyle.bg}`}>
                        <StatusIcon className={`w-4 h-4 ${statusStyle.color}`} />
                        <span className={`text-sm font-medium ${statusStyle.color}`}>
                          {statusStyle.label}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      {order.items.map((item) => (
                        <div key={item.id} className="flex justify-between text-sm">
                          <span className="text-gray-600">{item.quantity}x {item.product.name}</span>
                          <span className="font-medium text-gray-800">₹{item.price}</span>
                        </div>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="text-lg font-bold text-gray-900">
                        Total: ₹{order.total.toFixed(2)}
                      </div>
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => handleViewDetails(order.id)}
                          className="flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors"
                        >
                          <Eye className="w-4 h-4" />
                          <span>Details</span>
                        </button>
                        {order.status === 'DELIVERED' && (
                          <button 
                            onClick={() => handleReorder(order)}
                            className="flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium hover:bg-green-200 transition-colors"
                          >
                            <RotateCcw className="w-4 h-4" />
                            <span>Reorder</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No orders found</h3>
              <p className="text-gray-600 mb-6">No orders match the selected filter</p>
              <button
                onClick={() => {
                  setSelectedFilter('all');
                  setPage(1);
                }}
                className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
              >
                View All Orders
              </button>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6 space-x-2">
              <button
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
                className={`px-4 py-2 rounded-lg ${
                  page === 1 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Previous
              </button>
              <span className="px-4 py-2 text-gray-700">
                Page {page} of {totalPages}
              </span>
              <button
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className={`px-4 py-2 rounded-lg ${
                  page === totalPages 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Next
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <button
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
          </div>

          <h1 className="text-4xl font-bold text-gray-800 mb-8">Order History</h1>

          {/* Summary Stats */}
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8 mb-8">
            <div className="grid grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">{orders.length}</div>
                <div className="text-gray-600">Total Orders</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">₹{totalSpent.toFixed(2)}</div>
                <div className="text-gray-600">Total Spent</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {orders.filter(o => o.status === 'DELIVERED').length}
                </div>
                <div className="text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  ₹{orders.length > 0 ? (totalSpent / orders.length).toFixed(2) : '0.00'}
                </div>
                <div className="text-gray-600">Average Order</div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex space-x-4 mb-8">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => {
                  setSelectedFilter(filter.id);
                  setPage(1);
                }}
                className={`flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-colors ${
                  selectedFilter === filter.id
                    ? 'bg-green-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                }`}
              >
                <span>{filter.label}</span>
                <span className={`px-3 py-1 rounded-full text-sm ${
                  selectedFilter === filter.id
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {filter.count}
                </span>
              </button>
            ))}
          </div>

          {/* Orders List */}
          {filteredOrders.length > 0 ? (
            <div className="space-y-6">
              {filteredOrders.map((order) => {
                const StatusIcon = statusConfig[order.status].icon;
                const statusStyle = statusConfig[order.status];
                
                return (
                  <div key={order.id} className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">Order #{order.orderNumber}</h3>
                        <p className="text-gray-600">Ordered on {new Date(order.createdAt).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}</p>
                      </div>
                      <div className={`flex items-center space-x-3 px-4 py-2 rounded-xl ${statusStyle.bg}`}>
                        <StatusIcon className={`w-5 h-5 ${statusStyle.color}`} />
                        <span className={`font-medium ${statusStyle.color}`}>
                          {statusStyle.label}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-8 mb-6">
                      <div>
                        <h4 className="font-medium text-gray-800 mb-3">Items Ordered</h4>
                        <div className="space-y-2">
                          {order.items.map((item) => (
                            <div key={item.id} className="flex justify-between">
                              <span className="text-gray-600">{item.quantity}x {item.product.name}</span>
                              <span className="font-medium text-gray-800">₹{item.price}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-gray-800 mb-3">Order Summary</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-gray-600">
                            <span>Subtotal</span>
                            <span>₹{order.subtotal.toFixed(2)}</span>
                          </div>
                          {order.couponDiscount > 0 && (
                            <div className="flex justify-between text-gray-600">
                              <span>Discount</span>
                              <span className="text-green-600">-₹{order.couponDiscount.toFixed(2)}</span>
                            </div>
                          )}
                          {order.paymentMethod === 'COD' && (
                            <div className="flex justify-between text-gray-600">
                              <span>COD Charges</span>
                              <span>₹50.00</span>
                            </div>
                          )}
                          <div className="flex justify-between text-gray-600">
                            <span>Shipping</span>
                            <span>Free</span>
                          </div>
                          <div className="flex justify-between font-semibold text-gray-900 pt-2 border-t border-gray-200">
                            <span>Total</span>
                            <span>₹{order.total.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        Payment: <span className="font-medium">{order.paymentMethod === 'COD' ? 'Cash on Delivery' : 'Online'}</span>
                      </div>
                      <div className="flex space-x-3">
                        <button 
                          onClick={() => handleViewDetails(order.id)}
                          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
                        >
                          <Eye className="w-4 h-4" />
                          <span>View Details</span>
                        </button>
                        {order.status === 'DELIVERED' && (
                          <button 
                            onClick={() => handleReorder(order)}
                            className="flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors"
                          >
                            <RotateCcw className="w-4 h-4" />
                            <span>Reorder</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-16">
              <Package className="w-24 h-24 text-gray-300 mx-auto mb-6" />
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">No orders found</h3>
              <p className="text-gray-600 mb-8">No orders match the selected filter</p>
              <button
                onClick={() => {
                  setSelectedFilter('all');
                  setPage(1);
                }}
                className="bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors"
              >
                View All Orders
              </button>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8 space-x-2">
              <button
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
                className={`px-6 py-3 rounded-xl font-medium ${
                  page === 1 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Previous
              </button>
              <span className="px-6 py-3 text-gray-700 font-medium">
                Page {page} of {totalPages}
              </span>
              <button
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className={`px-6 py-3 rounded-xl font-medium ${
                  page === totalPages 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Next
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderHistory;